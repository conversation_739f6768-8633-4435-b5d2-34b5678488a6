// 使用示例 - 如何集成和使用 update_exe 类

#include <iostream>
#include <httplib.h>
#include "update_exe.h"
#include "dbi.h"  // 假设你有这个数据库接口类

int main() {
    try {
        // 1. 初始化数据库接口
        DBI* database = new DBI();  // 根据你的实际DBI类实现
        
        // 2. 创建更新执行器
        update_exe updater(database);
        
        // 3. 创建HTTP服务器
        httplib::Server server;
        
        // 4. 绑定路由
        updater.bindServe(server);
        
        // 5. 添加其他路由（可选）
        server.Get("/", [](const httplib::Request&, httplib::Response& res) {
            res.set_content("Database Update Service is running", "text/plain");
        });
        
        // 6. 启动服务器
        std::cout << "启动数据库更新服务..." << std::endl;
        std::cout << "服务地址: http://localhost:8080" << std::endl;
        std::cout << "更新接口: POST http://localhost:8080/sys/misc/update" << std::endl;
        
        if (!server.listen("0.0.0.0", 8080)) {
            std::cerr << "无法启动服务器" << std::endl;
            return -1;
        }
        
        // 7. 清理资源
        delete database;
        
    } catch (const std::exception& e) {
        std::cerr << "程序异常: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}

// 如果你想要直接调用更新功能而不通过HTTP服务，可以这样做：
void direct_update_example() {
    try {
        DBI* database = new DBI();
        update_exe updater(database);
        
        // 创建模拟的HTTP请求和响应
        httplib::Request req;
        httplib::Response res;
        
        // 直接调用处理函数
        updater.handle(req, res);
        
        // 检查结果
        if (res.status == 200) {
            std::cout << "更新成功: " << res.body << std::endl;
        } else {
            std::cout << "更新失败: " << res.body << std::endl;
        }
        
        delete database;
        
    } catch (const std::exception& e) {
        std::cerr << "直接更新异常: " << e.what() << std::endl;
    }
}
