import sqlite3
import sys
import os

# ### 配置区域 ###
# --- 以下是根据您的 Windows 环境更新的路径 ---

# 1. 旧数据库的文件路径 (Windows)
# 注意：在Python中，使用正斜杠 / 是处理Windows路径最推荐的方式
OLD_DB_PATH = 'C:/Users/<USER>/Desktop/updatetool/press.lx'

# 2. 新数据库的文件路径 (Windows)
NEW_DB_PATH = 'C:/Users/<USER>/Desktop/updatetool/newpress/press.lx'

# 3. 需要操作的表名列表 (清空并复制)
TABLES_TO_PROCESS = [
    "profile",
    "DeviceConfig",
    "HardwareConfiguration",
    "UnitSysConfig"
]

# 4. 特殊表处理配置 - 如果某些表有已知问题，可以在这里配置排除的列
SPECIAL_TABLE_CONFIG = {
    # 示例：如果 profile 表的某些列有问题，可以指定要排除的列
    # "profile": {
    #     "exclude_columns": ["password_hash", "created_md5"]  # 排除包含md5的列
    # }
}

# --- 配置结束 ---


def check_files_and_permissions():
    """检查数据库文件是否存在以及脚本是否有权限读写。"""
    # 检查旧数据库文件是否存在
    if not os.path.exists(OLD_DB_PATH):
        print(f"错误: 旧数据库文件不存在于 '{OLD_DB_PATH}'", file=sys.stderr)
        return False
    # 检查新数据库文件是否存在
    if not os.path.exists(NEW_DB_PATH):
        print(f"错误: 新数据库文件不存在于 '{NEW_DB_PATH}'", file=sys.stderr)
        print("请确保新数据库文件已创建并且路径正确。", file=sys.stderr)
        return False
        
    # 检查对文件的读写权限
    if not os.access(OLD_DB_PATH, os.R_OK):
        print(f"权限错误: 脚本没有读取旧数据库 '{OLD_DB_PATH}' 的权限。", file=sys.stderr)
        return False
    if not os.access(NEW_DB_PATH, os.W_OK):
        print(f"权限错误: 脚本没有写入新数据库 '{NEW_DB_PATH}' 的权限。", file=sys.stderr)
        return False
        
    return True


def transfer_db_data(old_db, new_db, table_list):
    """
    清空新数据库中的指定表，然后使用 ATTACH DATABASE 高效地从旧数据库复制数据。
    整个过程在一个事务中完成，保证原子性。

    :param old_db: 旧数据库的文件路径。
    :param new_db: 新数据库的文件路径。
    :param table_list: 需要清空并复制的表名列表。
    """
    if not check_files_and_permissions():
        print("\n操作中止。请检查文件路径和读写权限。", file=sys.stderr)
        sys.exit(1)

    try:
        # 1. 连接到新数据库，'with'语句会自动处理事务
        with sqlite3.connect(new_db) as conn:
            cursor = conn.cursor()

            # 2. 【步骤 1】在复制前，先清空新数据库中的目标表
            print("--- 步骤 1: 正在清空新数据库中的目标表 ---")
            for table_name in table_list:
                print(f"  -> 正在删除表 '{table_name}' 中的所有数据...")
                # 使用双引号确保表名被正确处理
                delete_query = f'DELETE FROM "{table_name}"'
                cursor.execute(delete_query)
            print("所有目标表已清空。\n")

            # 3. 【步骤 2】附加旧数据库，并为其设置一个别名 'old_db'
            print("--- 步骤 2: 正在从旧数据库复制数据 ---")
            print(f"正在附加旧数据库: {old_db}")
            cursor.execute("ATTACH DATABASE ? AS old_db", (old_db,))

            # 4. 遍历表列表，从旧库复制数据到新库
            for table_name in table_list:
                print(f"  -> 正在复制数据到表 '{table_name}'...")
                try:
                    query = f'INSERT INTO main."{table_name}" SELECT * FROM old_db."{table_name}"'
                    cursor.execute(query)
                except sqlite3.OperationalError as table_error:
                    if "no such function: md5" in str(table_error):
                        print(f"    警告: 表 '{table_name}' 包含 md5 函数，尝试手动复制兼容的列...")
                        # 获取新表的列信息
                        cursor.execute(f'PRAGMA table_info(main."{table_name}")')
                        new_columns = [row[1] for row in cursor.fetchall()]

                        # 获取旧表的列信息
                        cursor.execute(f'PRAGMA table_info(old_db."{table_name}")')
                        old_columns = [row[1] for row in cursor.fetchall()]

                        # 找到共同的列，并排除配置中指定的列
                        common_columns = [col for col in new_columns if col in old_columns]

                        # 如果有特殊配置，排除指定的列
                        if table_name in SPECIAL_TABLE_CONFIG:
                            exclude_cols = SPECIAL_TABLE_CONFIG[table_name].get("exclude_columns", [])
                            common_columns = [col for col in common_columns if col not in exclude_cols]
                            if exclude_cols:
                                print(f"    根据配置排除了列: {exclude_cols}")

                        if common_columns:
                            columns_str = ', '.join([f'"{col}"' for col in common_columns])
                            query = f'INSERT INTO main."{table_name}" ({columns_str}) SELECT {columns_str} FROM old_db."{table_name}"'
                            cursor.execute(query)
                            print(f"    成功复制了 {len(common_columns)} 个兼容列")
                        else:
                            print(f"    错误: 表 '{table_name}' 没有找到兼容的列")
                            raise table_error
                    else:
                        raise table_error

            # 5. 分离旧数据库
            print("正在分离旧数据库...")
            cursor.execute("DETACH DATABASE old_db")

            # 'with' 语句将在代码块结束时自动提交事务（commit）
            print("\n操作成功完成！数据已清空并从旧数据库重新载入。")

    except sqlite3.OperationalError as e:
        print(f"\n发生操作错误: {e}", file=sys.stderr)
        print("操作已自动回滚。新数据库未被修改。", file=sys.stderr)
        print("请检查：", file=sys.stderr)
        print(f"  - 数据库路径 '{old_db}' 和 '{new_db}' 是否正确？", file=sys.stderr)
        print(f"  - 表名 {table_list} 是否都存在于两个数据库中？", file=sys.stderr)
    except Exception as e:
        print(f"\n发生了未预料的错误: {e}", file=sys.stderr)
        print("操作已自动回滚。新数据库未被修改。", file=sys.stderr)


if __name__ == '__main__':
    transfer_db_data(OLD_DB_PATH, NEW_DB_PATH, TABLES_TO_PROCESS)