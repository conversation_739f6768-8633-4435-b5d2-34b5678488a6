import sqlite3
import sys
import os
import hashlib

# ### 配置区域 ###
OLD_DB_PATH = 'C:/Users/<USER>/Desktop/updatetool/press.lx'
NEW_DB_PATH = 'C:/Users/<USER>/Desktop/updatetool/newpress/press.lx'

TABLES_TO_PROCESS = [
    "profile",
    "DeviceConfig", 
    "HardwareConfiguration",
    "UnitSysConfig"
]

# 触发器处理选项
TRIGGER_OPTIONS = {
    "disable_during_copy": True,  # 复制时禁用触发器
    "register_md5": True,         # 注册md5函数
    "clear_trigger_tables": True  # 清空触发器相关表
}

# 触发器相关的表（需要一起清空）
TRIGGER_RELATED_TABLES = [
    "profilemd5",
    "profilerecord"
]

def md5_function(text):
    """SQLite自定义md5函数实现"""
    if text is None:
        return None
    return hashlib.md5(str(text).encode('utf-8')).hexdigest()

def register_md5_function(conn):
    """在SQLite连接中注册md5函数"""
    conn.create_function("md5", 1, md5_function)
    print("✅ 已注册自定义md5函数")

def check_files_and_permissions():
    """检查数据库文件是否存在以及脚本是否有权限读写。"""
    if not os.path.exists(OLD_DB_PATH):
        print(f"❌ 错误: 旧数据库文件不存在于 '{OLD_DB_PATH}'", file=sys.stderr)
        return False
    if not os.path.exists(NEW_DB_PATH):
        print(f"❌ 错误: 新数据库文件不存在于 '{NEW_DB_PATH}'", file=sys.stderr)
        return False
    if not os.access(OLD_DB_PATH, os.R_OK):
        print(f"❌ 权限错误: 脚本没有读取旧数据库的权限", file=sys.stderr)
        return False
    if not os.access(NEW_DB_PATH, os.W_OK):
        print(f"❌ 权限错误: 脚本没有写入新数据库的权限", file=sys.stderr)
        return False
    return True

def get_trigger_info(conn):
    """获取数据库中的触发器信息"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT name, tbl_name, sql 
        FROM sqlite_master 
        WHERE type = 'trigger' AND tbl_name IN ({})
    """.format(','.join(['?' for _ in TABLES_TO_PROCESS])), TABLES_TO_PROCESS)
    
    triggers = cursor.fetchall()
    if triggers:
        print(f"📋 发现 {len(triggers)} 个相关触发器:")
        for name, table, sql in triggers:
            print(f"  - {name} (表: {table})")
    return triggers

def disable_triggers(conn, triggers):
    """禁用指定的触发器"""
    cursor = conn.cursor()
    disabled_triggers = []
    
    for name, table, sql in triggers:
        try:
            cursor.execute(f"DROP TRIGGER IF EXISTS {name}")
            disabled_triggers.append((name, table, sql))
            print(f"  ⏸️  已禁用触发器: {name}")
        except Exception as e:
            print(f"  ❌ 禁用触发器失败 {name}: {e}")
    
    return disabled_triggers

def restore_triggers(conn, triggers):
    """恢复触发器"""
    cursor = conn.cursor()
    
    for name, table, sql in triggers:
        try:
            cursor.execute(sql)
            print(f"  ✅ 已恢复触发器: {name}")
        except Exception as e:
            print(f"  ❌ 恢复触发器失败 {name}: {e}")

def clear_trigger_related_tables(conn):
    """清空触发器相关的表"""
    cursor = conn.cursor()
    
    for table_name in TRIGGER_RELATED_TABLES:
        try:
            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            
            if cursor.fetchone():
                cursor.execute(f'DELETE FROM "{table_name}"')
                print(f"  🗑️  已清空触发器相关表: {table_name}")
            else:
                print(f"  ℹ️  表不存在，跳过: {table_name}")
        except Exception as e:
            print(f"  ❌ 清空表失败 {table_name}: {e}")

def transfer_db_data_with_triggers(old_db, new_db, table_list):
    """
    支持触发器的数据库迁移函数
    """
    if not check_files_and_permissions():
        print("\n❌ 操作中止。请检查文件路径和读写权限。", file=sys.stderr)
        sys.exit(1)

    conn = None
    disabled_triggers = []
    
    try:
        # 1. 连接到新数据库
        conn = sqlite3.connect(new_db)
        cursor = conn.cursor()
        
        # 2. 注册md5函数
        if TRIGGER_OPTIONS["register_md5"]:
            register_md5_function(conn)
        
        # 设置忙等待超时
        conn.execute("PRAGMA busy_timeout = 3000")
        
        # 开始事务
        conn.execute("BEGIN IMMEDIATE")
        print("🔄 事务已开始")

        # 3. 获取触发器信息
        print("\n📋 --- 步骤 1: 分析触发器 ---")
        triggers = get_trigger_info(conn)

        # 4. 禁用触发器（如果需要）
        if TRIGGER_OPTIONS["disable_during_copy"] and triggers:
            print("\n⏸️  --- 步骤 2: 禁用触发器 ---")
            disabled_triggers = disable_triggers(conn, triggers)

        # 5. 清空目标表
        print("\n🗑️  --- 步骤 3: 清空目标表 ---")
        
        # 清空触发器相关表
        if TRIGGER_OPTIONS["clear_trigger_tables"]:
            print("清空触发器相关表:")
            clear_trigger_related_tables(conn)
        
        # 清空主要表
        print("清空主要表:")
        for table_name in table_list:
            print(f"  -> 正在删除表 '{table_name}' 中的所有数据...")
            cursor.execute(f'DELETE FROM "{table_name}"')

        # 6. 附加旧数据库
        print(f"\n🔗 --- 步骤 4: 附加旧数据库 ---")
        print(f"正在附加旧数据库: {old_db}")
        cursor.execute("ATTACH DATABASE ? AS old_db", (old_db,))

        # 7. 复制数据
        print(f"\n📋 --- 步骤 5: 复制数据 ---")
        for table_name in table_list:
            print(f"  -> 正在复制数据到表 '{table_name}'...")
            try:
                query = f'INSERT INTO main."{table_name}" SELECT * FROM old_db."{table_name}"'
                cursor.execute(query)
                
                # 获取复制的行数
                row_count = cursor.rowcount
                print(f"     ✅ 成功复制 {row_count} 行数据")
                
            except sqlite3.OperationalError as e:
                if "no such function: md5" in str(e):
                    print(f"     ⚠️  检测到md5函数错误，尝试兼容模式...")
                    # 这里可以添加兼容处理逻辑
                    raise e
                else:
                    raise e

        # 8. 恢复触发器
        if disabled_triggers:
            print(f"\n🔄 --- 步骤 6: 恢复触发器 ---")
            restore_triggers(conn, disabled_triggers)

        # 9. 分离旧数据库
        print(f"\n🔌 --- 步骤 7: 分离旧数据库 ---")
        for attempt in range(3):
            try:
                cursor.execute("DETACH DATABASE old_db")
                print("  ✅ 成功分离数据库")
                break
            except sqlite3.OperationalError as e:
                if "locked" in str(e).lower() and attempt < 2:
                    print(f"  ⏳ 尝试 {attempt + 1}: 数据库被锁定，等待1秒后重试...")
                    import time
                    time.sleep(1)
                else:
                    raise e

        # 10. 提交事务
        conn.commit()
        print(f"\n✅ 操作成功完成！数据已清空并从旧数据库重新载入。")
        
        # 显示统计信息
        print(f"\n📊 操作统计:")
        print(f"  - 处理表数量: {len(table_list)}")
        print(f"  - 处理触发器数量: {len(triggers)}")
        if TRIGGER_OPTIONS["clear_trigger_tables"]:
            print(f"  - 清空触发器相关表数量: {len(TRIGGER_RELATED_TABLES)}")

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"\n❌ 发生错误: {e}", file=sys.stderr)
        print("操作已自动回滚。新数据库未被修改。", file=sys.stderr)
        
        # 尝试恢复触发器
        if disabled_triggers and conn:
            print("🔄 尝试恢复触发器...")
            try:
                restore_triggers(conn, disabled_triggers)
            except:
                print("⚠️  触发器恢复失败，可能需要手动检查")
                
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    print("=== 支持触发器的数据库迁移工具 ===")
    print(f"配置选项:")
    for key, value in TRIGGER_OPTIONS.items():
        print(f"  - {key}: {value}")
    print()
    
    transfer_db_data_with_triggers(OLD_DB_PATH, NEW_DB_PATH, TABLES_TO_PROCESS)
