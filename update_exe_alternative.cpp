// 替代方案：不手动分离数据库，让SQLite自动处理
// 这个版本在数据复制完成后直接提交事务，不手动分离数据库

void update_exe::handle(const httplib::Request& req, httplib::Response& res)
{
    sqlite3* db = nullptr;
    int rc;

    // 1. 连接到新数据库
    rc = sqlite3_open(NEW_DB_PATH.c_str(), &db);
    if (rc != SQLITE_OK) {
        std::cerr << "无法打开数据库: " << sqlite3_errmsg(db) << std::endl;
        if (db) sqlite3_close(db);
        return;
    }
    std::cout << "已成功连接到新数据库: " << NEW_DB_PATH << std::endl;

    // 设置忙等待超时
    sqlite3_busy_timeout(db, 5000);

    // 注册 MD5 函数
    rc = sqlite3_create_function(db, "md5", 1, SQLITE_UTF8, nullptr, md5, nullptr, nullptr);
    if (rc != SQLITE_OK) {
        std::cerr << "无法创建 md5 函数: " << sqlite3_errmsg(db) << std::endl;
        sqlite3_close(db);
        return;
    }

    bool success = true;

    try {
        // 开始事务
        if (!execute_sql(db, "BEGIN IMMEDIATE;")) {
            throw std::runtime_error("无法开始事务");
        }

        // 清空目标表
        std::cout << "--- 步骤 1: 清空目标表 ---" << std::endl;
        for (const auto& table_name : TABLES_TO_PROCESS) {
            std::cout << "  -> 清空表 '" << table_name << "'" << std::endl;
            std::string delete_sql = "DELETE FROM \"" + table_name + "\";";
            if (!execute_sql(db, delete_sql)) {
                throw std::runtime_error("清空表失败: " + table_name);
            }
        }

        // 附加旧数据库
        std::cout << "--- 步骤 2: 附加旧数据库 ---" << std::endl;
        std::string attach_sql = "ATTACH DATABASE '" + OLD_DB_PATH + "' AS old_db;";
        if (!execute_sql(db, attach_sql)) {
            throw std::runtime_error("附加数据库失败");
        }

        // 复制数据
        std::cout << "--- 步骤 3: 复制数据 ---" << std::endl;
        for (const auto& table_name : TABLES_TO_PROCESS) {
            std::cout << "  -> 复制表 '" << table_name << "'" << std::endl;
            std::string insert_sql = "INSERT INTO \"" + table_name + "\" SELECT * FROM old_db.\"" + table_name + "\";";
            if (!execute_sql(db, insert_sql)) {
                throw std::runtime_error("复制数据失败: " + table_name);
            }
        }

        // 提交事务（不手动分离数据库）
        std::cout << "--- 步骤 4: 提交事务 ---" << std::endl;
        if (!execute_sql(db, "COMMIT;")) {
            throw std::runtime_error("提交事务失败");
        }

        std::cout << "\n✅ 操作成功完成！" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "\n❌ 操作失败: " << e.what() << std::endl;
        execute_sql(db, "ROLLBACK;");
        success = false;
    }

    // 关闭数据库连接（SQLite会自动分离附加的数据库）
    sqlite3_close(db);
    
    if (success) {
        std::cout << "数据库连接已关闭，附加的数据库已自动分离。" << std::endl;
    }
}
