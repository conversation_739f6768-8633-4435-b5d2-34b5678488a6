#!/bin/bash

# 测试脚本 - 用于测试数据库更新功能

echo "=== 数据库更新功能测试 ==="

# 配置
OLD_DB="/root/press.lx"
NEW_DB="/root/press_new.lx"
BACKUP_DIR="/tmp/db_backup_$(date +%Y%m%d_%H%M%S)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查文件是否存在
check_file() {
    if [ -f "$1" ]; then
        print_success "文件存在: $1"
        return 0
    else
        print_error "文件不存在: $1"
        return 1
    fi
}

# 备份数据库
backup_databases() {
    print_info "创建备份目录: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    
    if [ -f "$OLD_DB" ]; then
        cp "$OLD_DB" "$BACKUP_DIR/press_old_backup.lx"
        print_success "旧数据库已备份"
    fi
    
    if [ -f "$NEW_DB" ]; then
        cp "$NEW_DB" "$BACKUP_DIR/press_new_backup.lx"
        print_success "新数据库已备份"
    fi
}

# 检查数据库表结构
check_table_structure() {
    local db_path="$1"
    local db_name="$2"
    
    print_info "检查 $db_name 数据库表结构..."
    
    if ! command -v sqlite3 &> /dev/null; then
        print_error "sqlite3 命令未找到，请安装 sqlite3"
        return 1
    fi
    
    # 检查 HardwareConfiguration 表是否存在
    local table_exists=$(sqlite3 "$db_path" "SELECT name FROM sqlite_master WHERE type='table' AND name='HardwareConfiguration';" 2>/dev/null)
    
    if [ -n "$table_exists" ]; then
        print_success "$db_name 中存在 HardwareConfiguration 表"
        
        # 显示表结构
        print_info "$db_name HardwareConfiguration 表结构:"
        sqlite3 "$db_path" ".schema HardwareConfiguration" 2>/dev/null || print_error "无法读取表结构"
        
        # 显示记录数
        local count=$(sqlite3 "$db_path" "SELECT COUNT(*) FROM HardwareConfiguration;" 2>/dev/null)
        print_info "$db_name HardwareConfiguration 表记录数: $count"
    else
        print_error "$db_name 中不存在 HardwareConfiguration 表"
        return 1
    fi
}

# 测试HTTP接口
test_http_interface() {
    print_info "测试HTTP接口..."
    
    # 检查服务是否运行
    if ! curl -s http://localhost:8080/ > /dev/null 2>&1; then
        print_error "HTTP服务未运行在端口8080"
        print_info "请先启动服务: sudo ./database_updater"
        return 1
    fi
    
    print_success "HTTP服务正在运行"
    
    # 发送更新请求
    print_info "发送更新请求..."
    response=$(curl -s -X POST http://localhost:8080/sys/misc/update)
    
    if [ $? -eq 0 ]; then
        print_success "HTTP请求成功"
        echo "响应: $response"
    else
        print_error "HTTP请求失败"
        return 1
    fi
}

# 主测试流程
main() {
    echo "开始测试..."
    echo "时间: $(date)"
    echo ""
    
    # 1. 检查必要文件
    print_info "步骤 1: 检查数据库文件"
    check_file "$OLD_DB" || exit 1
    check_file "$NEW_DB" || exit 1
    echo ""
    
    # 2. 备份数据库
    print_info "步骤 2: 备份数据库"
    backup_databases
    echo ""
    
    # 3. 检查表结构
    print_info "步骤 3: 检查数据库表结构"
    check_table_structure "$OLD_DB" "旧数据库"
    check_table_structure "$NEW_DB" "新数据库"
    echo ""
    
    # 4. 检查权限
    print_info "步骤 4: 检查文件权限"
    if [ -r "$OLD_DB" ]; then
        print_success "旧数据库可读"
    else
        print_error "旧数据库不可读"
    fi
    
    if [ -w "$NEW_DB" ]; then
        print_success "新数据库可写"
    else
        print_error "新数据库不可写"
    fi
    echo ""
    
    # 5. 测试HTTP接口（可选）
    print_info "步骤 5: 测试HTTP接口（可选）"
    test_http_interface
    echo ""
    
    print_success "测试完成！"
    print_info "备份位置: $BACKUP_DIR"
    
    echo ""
    echo "如果测试通过，你可以："
    echo "1. 直接运行程序: sudo ./database_updater"
    echo "2. 或通过HTTP接口调用更新功能"
}

# 运行主函数
main "$@"
