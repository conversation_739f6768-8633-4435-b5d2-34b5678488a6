#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EOCalc 配置解析测试
测试 read_EoConf 函数中 eo_type == 7 的处理逻辑
"""

import struct

def parse_eo_calc_config(rawBytes, offset, temp_data):
    """
    解析 EOCalc 配置数据 (eo_type == 7)
    
    根据协议包含:
    1. GroupCount (U8) - 组数量：0, 1, 或 2
    2. 对于每个组:
       - EOCalcVar1 (第一个操作变量)
       - EOCalcVar2 (第二个操作变量) 
       - OperationType (运算类型)
       - MinRange/MaxRange (范围设置)
    """
    
    print("开始解析 EOCalc 配置数据...")
    temp_data.append("=== EOCalc Configuration ===")
    
    # GroupCount (U8) - 组数量：0, 1, 或 2
    group_count = struct.unpack_from('<B', rawBytes, offset)[0]
    temp_data.append("GroupCount:")
    temp_data.append(group_count)
    offset += 1
    print(f"  GroupCount: {group_count}")
    
    # 定义结果值类型映射表
    eo_result_types = [
        "X Max-X", "X Max-Y", "Y Max-X", "Y Max-Y",
        "X Min-X", "X Min-Y", "Y Min-X", "Y Min-Y", 
        "Entry-X", "Entry-Y", "Exit-X", "Exit-Y",
        "PeakPeak-Y", "Average Y", "Min Time", "Max Time",
        "Min Speed", "Max Speed", "InflexCoordX", "InflexCoordY"
    ]
    
    curve_result_types = [
        "TimeMin_T", "TimeMin_X", "TimeMin_Force",
        "TimeMax_T", "TimeMax_X", "TimeMax_Force",
        "TimeAverage", "Xmin_T", "Xmin_X", "Xmin_Force",
        "Xmax_T", "Xmax_X", "Xmax_Force", "XAverage",
        "ForceMin_T", "ForceMin_X", "ForceMin_Force",
        "ForceMax_T", "ForceMax_X", "ForceMax_Force", "ForceAverage"
    ]
    
    operation_types = ["Addition", "Subtraction", "Multiplication", "Division"]
    
    # 处理每个 EOCalcGroup
    for group_idx in range(group_count):
        print(f"  解析第 {group_idx + 1} 个 EOCalcGroup...")
        temp_data.append(f"--- EOCalcGroup{group_idx + 1} ---")
        
        # ==========================================
        # EOCalcVar1 - 第一个操作变量
        # ==========================================
        
        # Source (U8) - 数据源类型
        source1 = struct.unpack_from('<B', rawBytes, offset)[0]
        temp_data.append("EOCalcVar1_Source:")
        if source1 <= 10:  # EO Index (0-10)
            temp_data.append(f"EO_{source1}")
            source1_type = "EO"
        elif source1 == 253:  # Curve
            temp_data.append("Curve")
            source1_type = "Curve"
        elif source1 == 255:  # Dynamic
            temp_data.append("Dynamic")
            source1_type = "Dynamic"
        else:
            temp_data.append(f"Unknown({source1})")
            source1_type = "Unknown"
        offset += 1
        print(f"    EOCalcVar1_Source: {source1} ({source1_type})")
        
        # ResultValueType (Variable) - 根据Source类型变化
        if source1 <= 10:  # EO Index
            result_value_type1 = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("EOCalcVar1_ResultValueType:")
            if result_value_type1 < len(eo_result_types):
                temp_data.append(eo_result_types[result_value_type1])
                print(f"    EOCalcVar1_ResultValueType: {eo_result_types[result_value_type1]}")
            else:
                temp_data.append(f"Unknown({result_value_type1})")
                print(f"    EOCalcVar1_ResultValueType: Unknown({result_value_type1})")
            offset += 1
        elif source1 == 253:  # Curve
            curve_result_type1 = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("EOCalcVar1_CurveResultValueType:")
            if curve_result_type1 < len(curve_result_types):
                temp_data.append(curve_result_types[curve_result_type1])
                print(f"    EOCalcVar1_CurveResultValueType: {curve_result_types[curve_result_type1]}")
            else:
                temp_data.append(f"Unknown({curve_result_type1})")
                print(f"    EOCalcVar1_CurveResultValueType: Unknown({curve_result_type1})")
            offset += 1
        elif source1 == 255:  # Dynamic
            temp_data.append("EOCalcVar1_DynamicVar:")
            temp_data.append("Dynamic Variable")
            print(f"    EOCalcVar1_DynamicVar: Dynamic Variable")
            # 动态变量可能需要额外的解析，这里暂时跳过
        
        # ==========================================
        # EOCalcVar2 - 第二个操作变量 (结构与EOCalcVar1相同)
        # ==========================================
        
        source2 = struct.unpack_from('<B', rawBytes, offset)[0]
        temp_data.append("EOCalcVar2_Source:")
        if source2 <= 10:  # EO Index (0-10)
            temp_data.append(f"EO_{source2}")
            source2_type = "EO"
        elif source2 == 253:  # Curve
            temp_data.append("Curve")
            source2_type = "Curve"
        elif source2 == 255:  # Dynamic
            temp_data.append("Dynamic")
            source2_type = "Dynamic"
        else:
            temp_data.append(f"Unknown({source2})")
            source2_type = "Unknown"
        offset += 1
        print(f"    EOCalcVar2_Source: {source2} ({source2_type})")
        
        # ResultValueType for Var2
        if source2 <= 10:  # EO Index
            result_value_type2 = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("EOCalcVar2_ResultValueType:")
            if result_value_type2 < len(eo_result_types):
                temp_data.append(eo_result_types[result_value_type2])
                print(f"    EOCalcVar2_ResultValueType: {eo_result_types[result_value_type2]}")
            else:
                temp_data.append(f"Unknown({result_value_type2})")
                print(f"    EOCalcVar2_ResultValueType: Unknown({result_value_type2})")
            offset += 1
        elif source2 == 253:  # Curve
            curve_result_type2 = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("EOCalcVar2_CurveResultValueType:")
            if curve_result_type2 < len(curve_result_types):
                temp_data.append(curve_result_types[curve_result_type2])
                print(f"    EOCalcVar2_CurveResultValueType: {curve_result_types[curve_result_type2]}")
            else:
                temp_data.append(f"Unknown({curve_result_type2})")
                print(f"    EOCalcVar2_CurveResultValueType: Unknown({curve_result_type2})")
            offset += 1
        elif source2 == 255:  # Dynamic
            temp_data.append("EOCalcVar2_DynamicVar:")
            temp_data.append("Dynamic Variable")
            print(f"    EOCalcVar2_DynamicVar: Dynamic Variable")
        
        # ==========================================
        # OperationType (U8) - 运算类型
        # ==========================================
        operation_type = struct.unpack_from('<B', rawBytes, offset)[0]
        temp_data.append("OperationType:")
        if operation_type < len(operation_types):
            temp_data.append(operation_types[operation_type])
            print(f"    OperationType: {operation_types[operation_type]}")
        else:
            temp_data.append(f"Unknown({operation_type})")
            print(f"    OperationType: Unknown({operation_type})")
        offset += 1
        
        # ==========================================
        # MinRange (F32) - 最小范围
        # ==========================================
        min_range = struct.unpack_from('<f', rawBytes, offset)[0]
        temp_data.append("MinRange:")
        temp_data.append(min_range)
        offset += 4
        print(f"    MinRange: {min_range}")
        
        # ==========================================
        # MaxRange (F32) - 最大范围
        # ==========================================
        max_range = struct.unpack_from('<f', rawBytes, offset)[0]
        temp_data.append("MaxRange:")
        temp_data.append(max_range)
        offset += 4
        print(f"    MaxRange: {max_range}")
    
    print("EOCalc 配置解析完成")
    return offset

def create_test_eo_calc_config():
    """
    创建测试用的 EOCalc 配置二进制数据
    """
    data = bytearray()
    
    # GroupCount = 2 (两个计算组)
    data.extend(struct.pack('<B', 2))
    
    # ==========================================
    # 第一个 EOCalcGroup
    # ==========================================
    
    # EOCalcVar1
    data.extend(struct.pack('<B', 1))    # Source = 1 (EO_1)
    data.extend(struct.pack('<B', 0))    # ResultValueType = 0 (X Max-X)
    
    # EOCalcVar2  
    data.extend(struct.pack('<B', 2))    # Source = 2 (EO_2)
    data.extend(struct.pack('<B', 1))    # ResultValueType = 1 (X Max-Y)
    
    # OperationType = 0 (Addition)
    data.extend(struct.pack('<B', 0))
    
    # MinRange = 10.0
    data.extend(struct.pack('<f', 10.0))
    
    # MaxRange = 100.0
    data.extend(struct.pack('<f', 100.0))
    
    # ==========================================
    # 第二个 EOCalcGroup
    # ==========================================
    
    # EOCalcVar1
    data.extend(struct.pack('<B', 253))  # Source = 253 (Curve)
    data.extend(struct.pack('<B', 5))    # CurveResultValueType = 5 (TimeMax_Force)
    
    # EOCalcVar2
    data.extend(struct.pack('<B', 253))  # Source = 253 (Curve)
    data.extend(struct.pack('<B', 10))   # CurveResultValueType = 10 (Xmax_Force)
    
    # OperationType = 2 (Multiplication)
    data.extend(struct.pack('<B', 2))
    
    # MinRange = 0.5
    data.extend(struct.pack('<f', 0.5))
    
    # MaxRange = 50.0
    data.extend(struct.pack('<f', 50.0))
    
    return bytes(data)

def test_eo_calc_config_parsing():
    """
    测试 EOCalc 配置解析功能
    """
    print("=" * 60)
    print("EOCalc 配置解析测试")
    print("=" * 60)
    
    # 创建测试数据
    test_data = create_test_eo_calc_config()
    print(f"测试数据长度: {len(test_data)} 字节")
    print(f"测试数据 (hex): {test_data.hex()}")
    print()
    
    # 解析数据
    offset = 0
    temp_data = []
    
    try:
        final_offset = parse_eo_calc_config(test_data, offset, temp_data)
        
        print("\n" + "=" * 60)
        print("解析结果输出")
        print("=" * 60)
        
        for i in range(0, len(temp_data), 2):
            if i + 1 < len(temp_data):
                print(f"{temp_data[i]} {temp_data[i+1]}")
            else:
                print(f"{temp_data[i]}")
        
        print(f"\n解析统计:")
        print(f"  起始offset: {offset}")
        print(f"  结束offset: {final_offset}")
        print(f"  数据总长度: {len(test_data)}")
        print(f"  解析字节数: {final_offset - offset}")
        
        if final_offset == len(test_data):
            print("  ✅ 解析成功，所有数据都被正确读取")
        else:
            print(f"  ❌ 解析可能有问题，剩余 {len(test_data) - final_offset} 字节未读取")
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_eo_calc_config_parsing()
