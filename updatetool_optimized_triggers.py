import sqlite3
import sys
import os
import hashlib
import time

# ### 配置区域 ###
OLD_DB_PATH = 'C:/Users/<USER>/Desktop/updatetool/press.lx'
NEW_DB_PATH = 'C:/Users/<USER>/Desktop/updatetool/newpress/press.lx'

TABLES_TO_PROCESS = [
    "profile",
    "DeviceConfig", 
    "HardwareConfiguration",
    "UnitSysConfig"
]

# 性能优化选项
PERFORMANCE_OPTIONS = {
    "batch_size": 1000,           # 批量处理大小
    "disable_triggers": True,     # 禁用触发器以提高性能
    "manual_trigger_logic": True, # 手动执行触发器逻辑
    "use_transaction": True,      # 使用事务
    "show_progress": True         # 显示进度
}

def md5_function(text):
    """SQLite自定义md5函数实现"""
    if text is None:
        return None
    return hashlib.md5(str(text).encode('utf-8')).hexdigest()

def register_md5_function(conn):
    """在SQLite连接中注册md5函数"""
    conn.create_function("md5", 1, md5_function)
    print("✅ 已注册自定义md5函数")

def get_table_count(conn, table_name, db_alias="main"):
    """获取表的记录数"""
    cursor = conn.cursor()
    cursor.execute(f'SELECT COUNT(*) FROM {db_alias}."{table_name}"')
    return cursor.fetchone()[0]

def disable_triggers_for_table(conn, table_name):
    """禁用指定表的触发器"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT name, sql FROM sqlite_master 
        WHERE type = 'trigger' AND tbl_name = ?
    """, (table_name,))
    
    triggers = cursor.fetchall()
    disabled_triggers = []
    
    for name, sql in triggers:
        try:
            cursor.execute(f"DROP TRIGGER IF EXISTS {name}")
            disabled_triggers.append((name, sql))
            print(f"  ⏸️  已禁用触发器: {name}")
        except Exception as e:
            print(f"  ❌ 禁用触发器失败 {name}: {e}")
    
    return disabled_triggers

def restore_triggers(conn, triggers):
    """恢复触发器"""
    cursor = conn.cursor()
    
    for name, sql in triggers:
        try:
            cursor.execute(sql)
            print(f"  ✅ 已恢复触发器: {name}")
        except Exception as e:
            print(f"  ❌ 恢复触发器失败 {name}: {e}")

def manual_profile_trigger_logic(conn, profile_data):
    """手动执行profile表触发器的逻辑"""
    cursor = conn.cursor()
    
    # 准备批量插入的数据
    profilemd5_data = []
    profilerecord_data = []
    
    for row in profile_data:
        # 假设profile表的列顺序（需要根据实际情况调整）
        # 这里需要根据你的实际表结构来映射
        update_time, fid, name, ver, eo, sequence, advanced_set, local_var, channel, id_generator, properties, operator, creation_time = row[:13]
        
        # 为profilemd5表准备数据
        fields = [
            ('EO', eo),
            ('Sequence', sequence),
            ('AdvancedSet', advanced_set),
            ('LocalVar', local_var),
            ('Channel', channel),
            ('IdGenerator', id_generator),
            ('Properties', properties)
        ]
        
        for field_name, content in fields:
            if content is not None:
                md5_hash = md5_function(content)
                profilemd5_data.append((md5_hash, content, field_name))
        
        # 为profilerecord表准备数据
        profilerecord_data.append((
            update_time,    # uniqueid
            'new',          # typecode
            fid,            # FID
            name,           # Name
            ver,            # Ver
            md5_function(eo),           # EO
            md5_function(sequence),     # Sequence
            md5_function(advanced_set), # AdvancedSet
            md5_function(local_var),    # LocalVar
            md5_function(channel),      # Channel
            md5_function(id_generator), # IdGenerator
            md5_function(properties),   # Properties
            operator,       # Operator
            creation_time   # CreationtimeId
        ))
    
    # 批量插入到profilemd5
    if profilemd5_data:
        cursor.executemany("""
            INSERT OR REPLACE INTO profilemd5 (md5code, content, field_name)
            VALUES (?, ?, ?)
        """, profilemd5_data)
        print(f"    📝 插入 {len(profilemd5_data)} 条记录到 profilemd5")
    
    # 批量插入到profilerecord
    if profilerecord_data:
        cursor.executemany("""
            INSERT OR IGNORE INTO profilerecord (
                uniqueid, typecode, FID, Name, Ver, EO, Sequence, AdvancedSet,
                LocalVar, Channel, IdGenerator, Properties, Operator, CreationtimeId
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, profilerecord_data)
        print(f"    📝 插入 {len(profilerecord_data)} 条记录到 profilerecord")

def copy_table_with_optimization(conn, table_name):
    """优化的表复制函数"""
    cursor = conn.cursor()
    
    # 获取源表记录数
    total_count = get_table_count(conn, table_name, "old_db")
    print(f"  📊 表 '{table_name}' 共有 {total_count} 条记录")
    
    if total_count == 0:
        print(f"  ⏭️  表 '{table_name}' 为空，跳过")
        return
    
    # 特殊处理profile表（有触发器）
    if table_name == "profile" and PERFORMANCE_OPTIONS["manual_trigger_logic"]:
        print(f"  🔧 使用优化模式处理 profile 表...")
        
        # 分批处理
        batch_size = PERFORMANCE_OPTIONS["batch_size"]
        processed = 0
        
        while processed < total_count:
            # 获取一批数据
            cursor.execute(f"""
                SELECT * FROM old_db."{table_name}" 
                LIMIT {batch_size} OFFSET {processed}
            """)
            batch_data = cursor.fetchall()
            
            if not batch_data:
                break
            
            # 插入到主表
            placeholders = ','.join(['?' for _ in batch_data[0]])
            cursor.executemany(f"""
                INSERT INTO main."{table_name}" VALUES ({placeholders})
            """, batch_data)
            
            # 手动执行触发器逻辑
            manual_profile_trigger_logic(conn, batch_data)
            
            processed += len(batch_data)
            
            if PERFORMANCE_OPTIONS["show_progress"]:
                progress = (processed / total_count) * 100
                print(f"    📈 进度: {processed}/{total_count} ({progress:.1f}%)")
    
    else:
        # 普通表的处理
        print(f"  📋 使用标准模式复制表 '{table_name}'...")
        cursor.execute(f'INSERT INTO main."{table_name}" SELECT * FROM old_db."{table_name}"')
        print(f"    ✅ 成功复制 {cursor.rowcount} 行数据")

def optimized_transfer_with_triggers(old_db, new_db, table_list):
    """优化的数据库迁移函数"""
    
    conn = None
    disabled_triggers = {}
    start_time = time.time()
    
    try:
        # 1. 连接数据库
        conn = sqlite3.connect(new_db)
        cursor = conn.cursor()
        
        # 注册md5函数
        register_md5_function(conn)
        
        # 优化设置
        conn.execute("PRAGMA busy_timeout = 5000")
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = NORMAL")
        conn.execute("PRAGMA cache_size = 10000")
        
        if PERFORMANCE_OPTIONS["use_transaction"]:
            conn.execute("BEGIN IMMEDIATE")
            print("🔄 事务已开始")

        # 2. 禁用触发器（如果需要）
        if PERFORMANCE_OPTIONS["disable_triggers"]:
            print("\n⏸️  --- 禁用触发器以提高性能 ---")
            for table_name in table_list:
                triggers = disable_triggers_for_table(conn, table_name)
                if triggers:
                    disabled_triggers[table_name] = triggers

        # 3. 清空目标表
        print("\n🗑️  --- 清空目标表 ---")
        for table_name in table_list:
            cursor.execute(f'DELETE FROM "{table_name}"')
            print(f"  ✅ 已清空表: {table_name}")
        
        # 清空触发器相关表
        trigger_tables = ["profilemd5", "profilerecord"]
        for table_name in trigger_tables:
            try:
                cursor.execute(f'DELETE FROM "{table_name}"')
                print(f"  ✅ 已清空触发器表: {table_name}")
            except:
                print(f"  ⏭️  表不存在，跳过: {table_name}")

        # 4. 附加旧数据库
        print(f"\n🔗 --- 附加旧数据库 ---")
        cursor.execute("ATTACH DATABASE ? AS old_db", (old_db,))

        # 5. 复制数据
        print(f"\n📋 --- 开始数据复制 ---")
        for table_name in table_list:
            print(f"\n处理表: {table_name}")
            copy_table_with_optimization(conn, table_name)

        # 6. 恢复触发器
        if disabled_triggers:
            print(f"\n🔄 --- 恢复触发器 ---")
            for table_name, triggers in disabled_triggers.items():
                if triggers:
                    print(f"恢复表 {table_name} 的触发器:")
                    restore_triggers(conn, triggers)

        # 7. 分离数据库
        print(f"\n🔌 --- 分离旧数据库 ---")
        cursor.execute("DETACH DATABASE old_db")

        # 8. 提交事务
        if PERFORMANCE_OPTIONS["use_transaction"]:
            conn.commit()
            print(f"\n✅ 事务已提交")

        # 性能统计
        end_time = time.time()
        duration = end_time - start_time
        print(f"\n📊 性能统计:")
        print(f"  - 总耗时: {duration:.2f} 秒")
        print(f"  - 处理表数: {len(table_list)}")
        print(f"  - 批量大小: {PERFORMANCE_OPTIONS['batch_size']}")

    except Exception as e:
        if conn and PERFORMANCE_OPTIONS["use_transaction"]:
            conn.rollback()
            print(f"\n❌ 发生错误，事务已回滚: {e}")
        
        # 尝试恢复触发器
        if disabled_triggers and conn:
            print("🔄 尝试恢复触发器...")
            for table_name, triggers in disabled_triggers.items():
                try:
                    restore_triggers(conn, triggers)
                except:
                    print(f"⚠️  表 {table_name} 的触发器恢复失败")
        raise e
                
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    print("=== 性能优化的数据库迁移工具 ===")
    print("配置选项:")
    for key, value in PERFORMANCE_OPTIONS.items():
        print(f"  - {key}: {value}")
    print()
    
    optimized_transfer_with_triggers(OLD_DB_PATH, NEW_DB_PATH, TABLES_TO_PROCESS)
