# 数据库更新工具 - 方案1（完整版）

这是一个用于SQLite数据库数据迁移的C++工具，专门解决数据库锁定问题，采用改进的清理和重试机制。

## 功能特性

- ✅ **智能重试机制**：自动处理数据库锁定问题，最多重试5次
- ✅ **内存清理**：在重试前执行SQLite内存清理和缓存管理
- ✅ **事务安全**：使用IMMEDIATE事务确保数据一致性
- ✅ **HTTP接口**：提供RESTful API进行远程调用
- ✅ **详细日志**：完整的操作日志和错误信息
- ✅ **CORS支持**：支持跨域请求

## 文件结构

```
├── update_exe_complete_v1.cpp  # 主要实现文件
├── update_exe.h                # 头文件
├── main_example.cpp            # 使用示例
├── compile.sh                  # 编译脚本
├── test_update.sh              # 测试脚本
└── README.md                   # 说明文档
```

## 配置说明

在 `update_exe_complete_v1.cpp` 中修改以下常量：

```cpp
const std::string OLD_DB_PATH = "/root/press.lx";        // 旧数据库路径
const std::string NEW_DB_PATH = "/root/press_new.lx";    // 新数据库路径
const std::vector<std::string> TABLES_TO_PROCESS = {     // 要处理的表
    "HardwareConfiguration",
};
```

## 编译和安装

### 1. 安装依赖

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install build-essential libsqlite3-dev
```

**CentOS/RHEL:**
```bash
sudo yum install gcc-c++ sqlite-devel
```

### 2. 获取httplib库
```bash
git clone https://github.com/yhirose/cpp-httplib.git
cp cpp-httplib/httplib.h .
```

### 3. 编译程序
```bash
chmod +x compile.sh
./compile.sh
```

## 使用方法

### 方法1：直接运行
```bash
sudo ./database_updater
```

### 方法2：HTTP接口调用
```bash
# 启动服务
sudo ./database_updater &

# 调用更新接口
curl -X POST http://localhost:8080/sys/misc/update
```

### 方法3：集成到现有项目
```cpp
#include "update_exe.h"

DBI* database = new DBI();
update_exe updater(database);

httplib::Request req;
httplib::Response res;
updater.handle(req, res);
```

## 测试

运行测试脚本检查环境和配置：

```bash
chmod +x test_update.sh
sudo ./test_update.sh
```

测试脚本会检查：
- 数据库文件是否存在
- 文件权限是否正确
- 表结构是否匹配
- HTTP服务是否正常

## 核心改进

### 1. 增强的重试机制
```cpp
bool detach_database_with_retry(sqlite3* db, const std::string& alias, int max_attempts = 5) {
    for (int attempt = 0; attempt < max_attempts; ++attempt) {
        if (attempt > 0) {
            // 执行清理操作
            sqlite3_db_release_memory(db);
            sqlite3_exec(db, "PRAGMA shrink_memory;", nullptr, 0, nullptr);
            std::this_thread::sleep_for(std::chrono::milliseconds(1500));
        }
        // 尝试分离数据库...
    }
}
```

### 2. 内存和缓存管理
```cpp
// 强制清理SQLite内部缓存和状态
execute_sql(db, "PRAGMA shrink_memory;");
execute_sql(db, "PRAGMA cache_size = 0;");
execute_sql(db, "PRAGMA cache_size = -2000;");
sqlite3_db_release_memory(db);
```

### 3. 改进的事务处理
```cpp
// 使用IMMEDIATE事务立即获得写锁
execute_sql(db, "BEGIN IMMEDIATE;");
sqlite3_busy_timeout(db, 3000);  // 3秒超时
```

## 故障排除

### 问题1：编译错误
```bash
# 检查是否安装了必要的开发库
sudo apt-get install build-essential libsqlite3-dev

# 检查httplib.h是否存在
ls -la httplib.h
```

### 问题2：权限错误
```bash
# 确保以root权限运行
sudo ./database_updater

# 检查数据库文件权限
ls -la /root/press*.lx
```

### 问题3：数据库仍然被锁定
```bash
# 检查是否有其他进程在使用数据库
lsof /root/press.lx
fuser /root/press.lx

# 终止占用进程
sudo fuser -k /root/press.lx
```

### 问题4：表结构不匹配
```bash
# 检查表结构
sqlite3 /root/press.lx ".schema HardwareConfiguration"
sqlite3 /root/press_new.lx ".schema HardwareConfiguration"
```

## API接口

### POST /sys/misc/update

**请求:**
```bash
curl -X POST http://localhost:8080/sys/misc/update \
     -H "Content-Type: application/json"
```

**成功响应:**
```json
{
    "status": "success",
    "message": "数据迁移完成"
}
```

**失败响应:**
```json
{
    "status": "error",
    "message": "数据迁移失败"
}
```

## 日志示例

```
已成功连接到新数据库: /root/press_new.lx
已成功注册自定义函数 'md5'。
事务已开始。

--- 步骤 1: 正在清空新数据库中的目标表 ---
  -> 正在删除表 'HardwareConfiguration' 中的所有数据...

--- 步骤 2: 正在从旧数据库复制数据 ---
正在附加旧数据库: /root/press.lx
  -> 正在复制数据到表 'HardwareConfiguration'...

正在准备分离旧数据库...
正在分离旧数据库...
  成功分离数据库

正在提交事务...

操作成功完成！数据已清空并从旧数据库重新载入。
```

## 注意事项

1. **备份重要**：运行前请备份数据库文件
2. **权限要求**：需要root权限访问/root目录下的文件
3. **单线程**：同时只能运行一个更新操作
4. **依赖关系**：确保dbi.h和md5函数的实现可用

## 许可证

本项目仅供学习和内部使用。
