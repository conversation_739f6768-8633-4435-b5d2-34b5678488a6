from math import fabs
from sre_constants import SUCCESS
# from cv2 import log
from numpy import full
from sqlalchemy import false
from watchdog.observers import Observer
from watchdog.events import *
import pandas as pd
import time
#import wmi
import os
import requests,os,uuid
import hashlib,struct
import re
import json
import shutil
import logging
import traceback
import datetime
import threading
import csv
import queue
import io
from sql import CsvDeal
from sql_result import ResultDeal
# from requests_toolbelt import MultipartEncoder
from xml.etree import ElementTree as ET
from multiprocessing import Process,Event
import random
#version": 3.7 describe:修改csv字段无,分割//3.7生成MD5位置改变

upload_file_success=True
g_state1 = False
i=0
Sys_Config_File_Dict = {'SysConfig':'GlobalSetting.cbin', 'GlobalVar':'GlobalVariable.gvar', 'FieldBus':'FielldbusFormat.fbf2'}

log_file_name=os.path.join(r'error.log')


creat_csv_is_error = False

csv_data_deal =  CsvDeal(r"sqlite:///C:\Press\tmp\csv\infrom\csvdb.db")
log_data_deal = ResultDeal(r"sqlite:///C:\Press\Result\result.db")
BASE_DIR = r'C:\Press'
BASE_TMP_DIR = os.path.join(BASE_DIR, 'tmp')
dbg=False #设置开启打印
Observer_DIR=r"C:/Press/tmp/Result"
g_srcfile=r''#log文件临时文件夹
g_dstfile=r'C:\Press\tmp\ErrResult'#异常log文件夹
g_confdir = r'C:\Press\tmp\Config'#log对应的配置文件夹
ip='127.0.0.1:80'

csv_path = r"C:\Press\tmp\csv\csv"
infrom_path = r"C:\Press\tmp\csv\infrom"

url = 'http://{0}/fileupload/'.format(ip)
url_queryConfigProfile='http://{0}/configProfileRecordExistQuery/'.format(ip)
mainboard_id=0
file_q = queue.Queue() #存放检测到的文件
g_csv_infrom={}

'''
logging.basicConfig(filename=log_file_name,filemode='a',
                     format=
                    '%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s'
                    )
'''
def check_and_rename(csv_filename,filename_part):
    # 如果文件名已经存在，并且是一个文件
    while os.path.exists(csv_filename) and os.path.isfile(csv_filename):
        # 在文件名的最后加上“-”符号和一个1到1000的随机数
        filename_part = filename_part + "-" + str(random.randint(1, 10000))
        csv_filename = os.path.join(csv_path,filename_part+'.csv')
    # 返回修改后的文件名
    return filename_part

def read_Resultltem(rawBytes, offset):
    ResultItem = {}
    varispass = struct.unpack_from('<B', rawBytes, offset)[0]
    offset+=1
    RealVar = struct.unpack_from('<f', rawBytes, offset)[0]
    offset +=4
    MinSet = struct.unpack_from('<f', rawBytes, offset)[0]
    offset +=4
    MaxSet = struct.unpack_from('<f', rawBytes, offset)[0]
    offset +=4
    ResultItem["Ispass"]=varispass
   
    ResultItem["Real Value"]=RealVar
    
    ResultItem["Min Range"]=MinSet
   
    ResultItem["Max Range"]=MaxSet
    
    return offset ,ResultItem
    
def read_upload_file_success():
    global upload_file_success
    return upload_file_success

def write_upload_file_success(value):
    global upload_file_success
    upload_file_success=value

def queryConfigProfile(sys_uuid, profile_uuid):
    #tmp,profileNames = getFileList(os.path.join(os.path.join(BASE_DIR, 'tmp', 'Config')))
    dic = {
        'SysUUID': sys_uuid,
        'ProfileUUID': profile_uuid,
    }
    r = requests.post(url_queryConfigProfile, data=dic)
    returnDict = json.loads(r.text)
    #print('queryConfigProfile return: ', returnDict)
    return returnDict['Sys_Record'] , returnDict['Profile_Record']
#更新nc的config信息
def updata_nc_config(conf_dic,uuids, model, sw_version,hd_version, config_file_version, sn, name, ip):
    dic = {
        'UploadType': '1',
        'model': model,
        'sw_version':sw_version,
        'hd_version' : hd_version,
        'config_file_version' : config_file_version,
        'sn': sn,
        'name': name,
        'ip':ip,
        'uuid': uuids[0],
        'sys_sdo_uuid': uuids[2],
        'global_var_uuid': uuids[3],
        'field_bus_uuid': uuids[4]
    }
    files = {}
    for item, fileName in Sys_Config_File_Dict.items():
        # fullName = os.path.join(os.path.join(BASE_TMP_DIR, 'Config'), fileName)
        file = io.BytesIO(conf_dic[fileName])
        files[item] = (fileName, file, 'application/vnd.global', {})
    #print('dic:', dic)
    r = requests.post(url, data=dic, files=files)
    returnDict = json.loads(r.text);
    #print('upload nc config: ',  returnDict)
    return returnDict['success']




    

def upload_profile(profile_dic, uuid, idx, name,eoCount, eoList):
    dic = {
        'UploadType': '2',
        'uuid': uuid,
        'idx': idx,
        'name': name,
        'eo_count': eoCount,
        'json_content': eoList
    }
    #print('upload_profile_name',fileName)
    file = io.BytesIO(profile_dic['data'])
    files = {'Profile': (profile_dic['name'], file, 'application/vnd.profile', {})}
    r = requests.post(url, files=files, data=dic)
    # r = requests.post(url, data=temp,headers={'Content-Type': temp.content_type})
    returnDict = json.loads(r.text)
    #print('upload profile: ', fileName, returnDict)
    return returnDict['success']


def write_log_infrom(name):
    global g_csv_infrom,log_data_deal
    log_data_deal.creat_session()
    log_data_deal.creat_table()
    log_data = {}
    log_data['is_pass'] = g_csv_infrom['is_pass']
    log_data['DeviceName'] = g_csv_infrom['DeviceName']
    log_data['TimeStamp'] =  g_csv_infrom['TimeStamp']
    log_data['nc_id'] = g_csv_infrom['nc_id']
    log_data["id"] = log_data_deal.get_new_id()
    log_data['csv_name'] = name
    try:
        success = log_data_deal.add_data(log_data)
        if(success == false):
            print("write log infrom sql fail")
    except:
        print("write log_infrom fail")
    log_data_deal.close()

def upload_product(fileName, uuids, sn, isPass, operator, op_finish_time, op_duration_time,
                   byte_offset_of_waveform,waveform_sample_count, json_result_list,
                   log_file_feature_data,python_data):
    error_flag = 0
    fullName = os.path.join(os.path.join(BASE_TMP_DIR,'Result'), fileName)
    if os.path.exists(fullName) and os.path.isfile(fullName):
            MaxSize = os.path.getsize(fullName)
            global mainboard_id
            sn_time=sn+op_finish_time
            sn_time_mainboard_id=sn_time+mainboard_id
            logfileMd5 = hashlib.md5(sn_time_mainboard_id.encode('utf-8')).hexdigest()
            log_name=sn+'-'+logfileMd5+'.log'
            dic = {
                'UploadType': '3',
                'uuid': logfileMd5,
                'nc_config_uuid': uuids[0],
                "profile_uuid": uuids[1],
                'sn': sn,
                'isPass': isPass,
                'operator': operator,
                'op_finish_time': op_finish_time,
                'op_duration_time': op_duration_time,
                'byte_offset_of_waveform':byte_offset_of_waveform,
                'waveform_sample_count':waveform_sample_count,
                'eo_result': json_result_list,
                'log_file_feature_data': log_file_feature_data,
            }
            for i in python_data :
                dic['{0}'.format(i)]=python_data[i]
            files = {'ResultLog': (log_name, open(fullName, 'rb'), 'application/vnd.resultLog', {})}
            r = requests.post(url, files=files, data=dic)
            returnDict = json.loads(r.text)
            if(returnDict['success']):
                write_log_infrom(log_name)
            else:
                print("上传 product fail")
            return returnDict['success']
    else:
        logging.error('upload_product fail:{0} no exists or no isfile').format(fullName)
        return False

def get_name_by_Idx(path_dir, profileIdx):
    if os.path.exists(path_dir):
        for filename in os.listdir(path_dir):
            fullName = os.path.join(path_dir, filename)
            if os.path.isfile(fullName):
                if re.search('^[0]*'+str(profileIdx), filename):
                    return filename

def getFileList(filePath):
    fileList = []
    profileList = []
    for fileName in os.listdir(filePath):
        fileFullName = os.path.join(filePath,fileName)
        if os.path.isfile(fileFullName):
            if re.search(r'^[\d]{3}_', fileName):
                profileList.append(fileName)
            else:
                fileList.append(fileName)
    return  fileList, profileList

def getFileMd5(contents):
    return  hashlib.md5(contents).hexdigest()
    # if os.path.isfile(fullName) and os.path.exists(fullName):
    #     try:
    #         with open(fullName, 'rb') as fp:
    #             contents = fp.read()
    #         md5 = hashlib.md5(contents).hexdigest()
    #         return md5
    #     except:
    #         logging.error('getFileMd5 fail,{0}生成md5失败'.format(fullName))
    # else:
    #     logging.error('getFileMd5 fail,{0}不存在或不是文件'.format(fullName))

def getProfileMd5(profile):
    try:
        contents=profile['data']+profile['name'].encode('utf-8')
        md5 = hashlib.md5(contents).hexdigest()
        return md5
    except:
        logging.error('getProfileMd5 fail ,{0}生成md5失败'.format(profile['name']))





def openProfileFile(fullName,FileData):
    eoType=''
    eoSharp='' 
    entryBorderStr=''
    exitBorderStr=''
    if fullName:
        fileNameParts =fullName.split('\\')[-1].split('.prof')[0].split('_',1)
        MaxSize =len(FileData)
        data = FileData
        # try:
        #     with open(fullName, 'rb') as f:
        #         data = f.read()
        # except:
        #     logging.error('openProfileFile fail,打开profile失败')
        offset = 0
        try:
            head, = struct.unpack_from('<8s', data, offset)
            #print(head.decode(encoding='ascii'))
            offset += 8+6
            binSize, = struct.unpack_from('<L', data, offset)
            offset += 4
            offset += binSize +102 #在新版里需加上
            #print('<{0}s'.format(MaxSize-offset))
            xmlString, = struct.unpack_from('<{0}s'.format(MaxSize-offset), data, offset)
            xmlString = xmlString.decode(encoding='utf-8')
            #print(xmlString)
            eoList=[]
            eoCount = 0
            root = ET.XML(xmlString)
            eoListNode = root.find(r'Evaluation').find('EvaluationObjectCollection')
            if eoListNode:
                for eoNode in eoListNode:
                    eoType = eoNode.tag#获取节点名称
                    eoName = eoNode.find('ShapeAdvancedProperties').find('Name').get('Value')
                    eoID = int(eoNode.find('Identity').get('Value'))
                    entryBorderStr = ''
                    exitBorderStr = ''
                    
                    if eoType == 'Rectangle':
                        RectanglePara = eoNode.find(r'Position').find('RectanglePosition')
                        xmin = float(RectanglePara.find('XMin').get('Value'))
                        xmax = float(RectanglePara.find('XMax').get('Value'))
                        ymin = float(RectanglePara.find('YMin').get('Value'))
                        ymax = float(RectanglePara.find('YMax').get('Value'))
                        eoSharp = [[xmin,ymin],[xmin,ymax],[xmax,ymax],[xmax,ymin],[xmin,ymin]]
                        entryBorderStr = RectanglePara.find('EntryType').get('Value')
                        entryBorderStr = RectanglePara.find('ExitType').get('Value')
                    elif eoType == 'HLine':
                        RectanglePara = eoNode.find(r'Position').find('HLinePosition')
                        xmin = float(RectanglePara.find('XMin').get('Value'))
                        xmax = float(RectanglePara.find('XMax').get('Value'))
                        ymin = float(RectanglePara.find('YPosition').get('Value'))
                        eoSharp = [[xmin,ymin],[xmax,ymin]]
                        entryBorderStr = RectanglePara.find('CrossType').get('Value')
                    elif eoType == 'VLine':
                        RectanglePara = eoNode.find(r'Position').find('VLinePosition')
                        xmin = float(RectanglePara.find('XPosition').get('Value'))
                        ymin = float(RectanglePara.find('YMin').get('Value'))
                        ymax = float(RectanglePara.find('YMax').get('Value'))
                        eoSharp = [[xmin,ymin],[xmin,ymax]]
                        entryBorderStr = RectanglePara.find('CrossType').get('Value')
                    ret = findBorder(eoType, eoSharp, entryBorderStr, exitBorderStr)
                    eoList.append({
                            'Name': eoName,
                            'Id': eoID,
                            'Type': eoType,
                            'EOSharp': eoSharp,
                            'RefCoordIdx': 0,
                            'Anchors': ret[0],
                            'HeadPosition': ret[1]
                            })
                    eoCount += 1
            else:
                #logging.error('openProfileFile():要解析的节点不存在')
                #return None
                pass
            Para = {'Idx': int(fileNameParts[0]),
                        'Name': fileNameParts[1],
                        'EOCount':eoCount,
                        'EOList': eoList
                        }
            return  Para
            
        except :
            logging.error('openProfileFile():{0}'.format(traceback.format_exc()))
    else:
        logging.error('openProfileFile fail,{0}不存在'.format(fullName))
#获取['GlobalSetting.cbin', 'GlobalVariable.gvar', 'FielldbusFormat.fbf2'],configSN,profileSN，五个文件的md5       
def check_config_profile_md5(conf_dic):
    if conf_dic['md5']:
        content = conf_dic['md5']
        arr = content.split('\n')
        if len(arr) == 5:
            return arr
        else:
            logging.error('check_config_profile_md5 file,md5数量不是5  number = {0}'.format(len(arr)))
            return None  
            #os.remove(os.path.join(configPath, '_NewFlag'))
    else:
        logging.error('check_config_profile_md5():fail')
        return None
#执行上传文件
def check_upload_product(conf_dic, uuids):
    global g_dstfile
    is_succeed=False
    error_flag = 0
    logFileName = conf_dic['log']
    try:
        fullName = os.path.join(os.path.join(BASE_DIR, 'tmp', 'Result'), logFileName)
        if os.path.exists(fullName):
            nc_config_combine_uuid = uuids[0]
            profile_uuid = uuids[1]
            error_flag = 1
            nc_sysSetting_ok, profile_record_ok = queryConfigProfile(nc_config_combine_uuid, profile_uuid)
            ret = True
            error_flag = 2
            dic_log = openResultlogFile(fullName,conf_dic)
            error_flag = 3
            if not nc_sysSetting_ok:
                error_flag = 4
                ret = ret and updata_nc_config(conf_dic['conf'],uuids, dic_log['nc_model'],dic_log['sw_version'],dic_log['hd_version'],dic_log['config_file_version'], dic_log['nc_id'], dic_log['DeviceName'], \
                                            dic_log['nc_ip'])
                if not ret:
                    #print('Fail to upload nc config file ')
                    logging.error('Fail to upload nc config file ')
            error_flag = 5
            if not profile_record_ok:
                # error_flag = 6
                # tmp,profileNames = getFileList(os.path.join(os.path.join(BASE_DIR, 'tmp', 'Config')))
                # error_flag = 7
                # profile_name = conf_dic['conf']['profile']['name']
                # profile_data = 
                dic = openProfileFile(conf_dic['conf']['profile']['name'],conf_dic['conf']['profile']['data'])
                error_flag = 8
                if dic:
                    error_flag = 9
                    #print('profile diction:', dic)
                    ret = ret and upload_profile(conf_dic['conf']['profile'], uuids[1], dic['Idx'], dic['Name'],
                                                dic['EOCount'], json.dumps(dic['EOList']))
                    error_flag = 10
                if not ret:
                    logging.error('Fail to upload profile file ')
            if ret:
                error_flag = 11
                log_file_feature_data={
                    'x_min'         : dic_log['x_min_x'],
                    'x_max'         : dic_log['x_max_x'],
                    'force_min'     : dic_log['force_min_force'],
                    'force_max'     : dic_log['force_max_force'],
                    'time_min'      : dic_log['time_min_t'],
                    'time_max'      : dic_log['time_max_t']
                }
                error_flag = 12
                log_file_feature_data=json.dumps(log_file_feature_data)
                error_flag = 13
                python_data_new={
                        'time_min_t'                :dic_log['time_min_t'],
                        'time_min_x'                :dic_log['time_min_x'],
                        'time_min_force'            :dic_log['time_min_force'],
                        'time_max_t'                :dic_log['time_max_t'],
                        'time_max_x'                :dic_log['time_max_x'],
                        'time_max_force'            :dic_log['time_max_force'],
                        'time_average'              :dic_log['time_average'],
                        'x_min_t'                   :dic_log['x_min_t'],
                        'x_min_x'                   :dic_log['x_min_x'],
                        'x_min_force'               :dic_log['x_min_force'],
                        'x_max_t'                   :dic_log['x_max_t'],
                        'x_max_x'                   :dic_log['x_max_x'],
                        'x_max_force'               :dic_log['x_max_force'],
                        'x_average'                 :dic_log['x_average'],
                        'force_min_t'               :dic_log['force_min_t'],
                        'force_min_x'               :dic_log['force_min_x'],
                        'force_min_force'           :dic_log['force_min_force'],
                        'force_max_t'               :dic_log['force_max_t'],
                        'force_max_x'               :dic_log['force_max_x'],
                        'force_max_force'           :dic_log['force_max_force'],
                        'force_average'             :dic_log['force_average']
                }
                error_flag = 14
                is_succeed=upload_product(logFileName, uuids, dic_log['sn'], dic_log['is_pass'], dic_log['Operator'], dic_log['TimeStamp'],
                            10, dic_log['RawWaveformByteOffset'], dic_log['SampleCount'], json.dumps(dic_log['ResultList']),log_file_feature_data,python_data_new)
                error_flag = 15
                # if is_succeed == False:
                #     g_srcfile = logFileName
                #     fpath,fname=os.path.split(g_srcfile)
                #     shutil.move(g_srcfile,os.path.join(g_dstfile,fname))
        else:
            logging.error('file not exists: {0},errorflag = {1}'.format(fullName,error_flag))
    except:
        logging.error('check_upload_product():{0},errorflag = {1}'.format(traceback.format_exc(),error_flag))
    if is_succeed == False:
        logging.error("check_upload_product():errorflag ={0}，name = {1}".format(error_flag,logFileName))
    return is_succeed

def check_fold_and_upload():
    tmpResultFilePath = os.path.join(BASE_TMP_DIR, 'Result')
    uuids = check_config_profile_md5()
    #print(uuids)
    fileList, tmp = getFileList(tmpResultFilePath)
    for logFileName in fileList:
        check_upload_product(logFileName, uuids)

def readToZero(rawBytes, RawBytesSize, offset):
    #print('Raw Data: ', rawBytes[0:255])
    offsetInner = 0
    while rawBytes[offset+offsetInner] != 0:
        offsetInner += 1
        if offsetInner>= 256:
            break
    #print('offsetInner: ', offsetInner)
    if offsetInner < 256:
        return rawBytes[offset:offset+offsetInner].decode(encoding='ascii'), offset+offsetInner+1

def findBorder(eoType, eoSharp,entryBorderStr, exitBorderStr):
    anchors = []
    eoHeadPosition = [[0,0]]
    if eoType == 'Rectangle':
        EntryBorderList = ['none', 'left', 'below', 'right', 'above','any']
        ExitBorderList = ['none', 'right', 'above', 'left', 'below','any']
        entryBorderStr = entryBorderStr.lower()
        exitBorderStr = exitBorderStr.lower()
        entrySymbolIndex = 0 if (entryBorderStr not in EntryBorderList) else EntryBorderList.index(entryBorderStr)
        exitSymbolIndex = 0 if exitBorderStr not in EntryBorderList else EntryBorderList.index(ExitBorderList)
        enP = [[0,0],  #EntryCenterPList
                [eoSharp[0][0], (eoSharp[0][1] + eoSharp[1][1])/2],
                [(eoSharp[0][0] + eoSharp[3][0]) / 2, eoSharp[0][1]],
                [eoSharp[2][0], (eoSharp[2][1] + eoSharp[3][1]) / 2],
                [(eoSharp[1][0] + eoSharp[2][0]) / 2, eoSharp[1][1]]]
        exP = [[0, 0], enP[3], enP[4], enP[1], enP[2]] #ExitCenterPList
        if entrySymbolIndex == 5:
            for i in range(1, 5):
                anchors.append({"Type": 1, "Center": [enP[1][0], enP[1][1]], "Rotation": 0})
        elif entrySymbolIndex < 5 and entrySymbolIndex > 0 :
            anchors.append({"Type": entrySymbolIndex, "Center": [enP[entrySymbolIndex][0], enP[entrySymbolIndex][1]], "Rotation": 0})

        if exitSymbolIndex == 5:
            for i in range(1, 5):
                anchors.append({"Type": 1, "Center": [exP[i][0], exP[i][1]], "Rotation": 0})
        elif exitSymbolIndex < 5 and exitSymbolIndex > 0:
            anchors.append({"Type": exitSymbolIndex, "Center": [exP[exitSymbolIndex][0], exP[exitSymbolIndex][1]], "Rotation": 0})
        eoHeadPosition = [eoSharp[1]]
    elif eoType == 'HLine':
        for i in range(1, 2):
            anchors.append({"Type": 7, "Center": eoSharp[0], "Rotation": 0})
            anchors.append({"Type": 7, "Center": eoSharp[1], "Rotation": 0})
        eoHeadPosition = [eoSharp[0]]
    elif eoType == 'VLine':
        for i in range(1, 2):
            anchors.append({"Type": 6, "Center": eoSharp[0], "Rotation": 0})
            anchors.append({"Type": 6, "Center": eoSharp[1], "Rotation": 0})
        eoHeadPosition = [eoSharp[1]]
    return anchors,eoHeadPosition

t_type = ['']
g_infrom_path =None
z_list=['Motor Encode','XExterSimu','TTL','Endata','Potentiometer']
y_list = ['Motor Encode','XExterSimu','TTL','Endata','Potentiometer']
force_unit = ['kN','N',"kgf"]
postion_unit=['mm','m','inch']
display_format_list = ['x','x.x','x.xx','x.xxx','x.xxxx','x.xxxxx','x.xxxxxx']
#0x02
def read_curve_data(rawBytes,RawBytesSize,offset):
    global force_unit,postion_unit,x_list,y_list,display_format_list
    x_index = 0#位移单位索引
    y_index = 0#力单位索引
    postion_display = 0
    force_displat = 0
    t_index = 5
    while True:
        identity=struct.unpack_from('<B', rawBytes, offset)[0]
        offset+=1
        data_len=struct.unpack_from('<L', rawBytes, offset)[0]
        offset+=4
        if identity==0x02:
            start_offset = offset
            print("curve_data数据长度",data_len)
            print("起始offset",offset)
            result={}
            cur_data = []
            temp_data = []
            temp_data.append("Curve Info")
            cur_data.append(temp_data)
            # result["SampleIndex"] = 
            temp_data = []
            temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("SampleIndex:")
            temp_data.append(temp)
            cur_data.append(temp_data)
            offset+=1
            # result["SampleRate"] = struct.unpack_from('<i', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<i', rawBytes, offset)[0]
            temp_data.append("SampleRate:")
            temp_data.append(temp)
            cur_data.append(temp_data)
            offset+=4
            # result["BeginTime"] = struct.unpack_from('<Q', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<Q', rawBytes, offset)[0]
            temp_data.append("BeginTime:")
            temp_data.append(temp)
            cur_data.append(temp_data)
            offset+=8

            # result["ChannelCount"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("ChannelCount:")
            temp_data.append(temp)
            cur_data.append(temp_data)
            offset+=1

            # result["ChannelType_x"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("ChannelType_x")
            temp_data.append("time")
            cur_data.append(temp_data)
            offset+=1

            # result["DataType_x"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("DisplayFormat:")
            temp_data.append(display_format_list[5])
            cur_data.append(temp_data)
            offset+=1

            # result["DataUnit_x"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("DataUnit:")
            temp_data.append('ms')
            cur_data.append(temp_data)
            offset+=1

            # result["ChannelType_y"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("ChannelType_y:")#postion
            temp_data.append(y_list[temp])
            cur_data.append(temp_data)
            offset+=1

            # result["DataType_y"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            postion_display = temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("DisplayFormat:")
            temp_data.append(display_format_list[temp])
            cur_data.append(temp_data)
            offset+=1

            # result["DataUnit_y"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            y_index=temp = struct.unpack_from('<B', rawBytes, offset)[0]#postion
            temp_data.append("DataUnit:")
            temp_data.append(postion_unit[temp])
            cur_data.append(temp_data)
            offset+=1

            # result["ChannelType_z"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("ChannelType_Z:")
            temp_data.append(z_list[temp])
            cur_data.append(temp_data)            
            offset+=1

            # result["DataType_z"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            force_display = temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("DisplayFormat:")
            temp_data.append(display_format_list[temp])
            cur_data.append(temp_data)  
            offset+=1

            # result["DataUnit_z"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            x_index = temp = struct.unpack_from('<B', rawBytes, offset)[0] #force
            temp_data.append("DataUnit:")
            temp_data.append(force_unit[temp])
            cur_data.append(temp_data)             
            offset+=1

            temp_data = []
            temp_data.append("")
            temp_len = len(cur_data)
            for i in range(18 -temp_len):
                cur_data.append(temp_data)

            result["SampleCount"] = struct.unpack_from('<L', rawBytes, offset)[0]
            temp_data = []
            temp = result["SampleCount"] 
            temp_data.append("SampleCount:")
            temp_data.append(temp)
            cur_data.append(temp_data)  
            offset+=4


            temp_data = []
            temp_data.append("Time: s")
            temp_data.append("Postion: {0}".format(postion_unit[y_index]))
            temp_data.append("Force: {0}".format(force_unit[x_index]))
            cur_data.append(temp_data)  


            if (result["SampleCount"] < 1):
                 break
            for i in range(result["SampleCount"]):
                temp_data =[]
                # temp_data.append("")
                temp =struct.unpack_from('<f', rawBytes, offset)[0] 
                temp_data.append(format(temp,'.5f'))
                # temp_data.append(temp)
                offset+=4
                temp =struct.unpack_from('<f', rawBytes, offset)[0] 
                temp_data.append(format(temp,'.5f'))
                offset+=4
                temp =struct.unpack_from('<f', rawBytes, offset)[0] 
                temp_data.append(format(temp,'.5f'))
                offset+=4
                cur_data.append(temp_data)
            print("结束offset",offset)
            if ((start_offset+data_len)!=offset):
                logging.error('read_curve_data check data len error satrt_offset = {0} data_len = {1} offset = {2}'.format(start_offset,data_len,offset))
                raise('error')
            break
        else:
            offset+=data_len
            if RawBytesSize==offset:
                logging.error('read_curve_data fail,没有获得需要的数据')
                raise Exception("没有获得需要的数据")
    return cur_data

#0x04
def read_ref_conf(rawBytes,RawBytesSize,offset):
    while True:
        identity=struct.unpack_from('<B', rawBytes, offset)[0]
        offset+=1
        data_len=struct.unpack_from('<L', rawBytes, offset)[0]
        offset+=4
        if identity==0x04:
            print("ref_conf数据长度",data_len)
            print("起始offset",offset)
            start_offset = offset
            result={}
            ref_conf = []
            temp_data = []
            temp_data.append("RefConf")
            ref_conf.append(temp_data)
            result["CustomRefCount"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp = result["CustomRefCount"]
            temp_data.append("RefCount:")
            temp_data.append(temp)
            ref_conf.append(temp_data)
            offset+=1
            if result["CustomRefCount"] < 0:
                temp_len = len(ref_conf)
                temp_data = []
                temp_data.append("")
                for i in range(20-temp_len):
                    ref_conf.append(temp_data)
                break
            # result["CustomRefID"] = struct.unpack_from('<B', rawBytes, offset)[0]
            j = 0
            for i in range(result["CustomRefCount"]):
                j+=1
                temp_data = []
                temp_data.append("Ref{0} ".format(j))
                CustomRefID = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                temp_data.append("CustomRefID:")
                temp_data.append(temp)  
                offset+=1

                temp = struct.unpack_from('<B', rawBytes, offset)[0]
                temp_data.append("RefID:")
                temp_data.append(temp)  
                offset+=1

                temp = struct.unpack_from('<B', rawBytes, offset)[0]
                temp_data.append("Global Hy:")
                temp_data.append(temp)  
                offset+=1

                temp = struct.unpack_from('<f', rawBytes, offset)[0]
                temp_data.append("RefxHysteresis:")
                temp_data.append(temp)           
                offset+=4

                temp = struct.unpack_from('<f', rawBytes, offset)[0]
                temp_data.append("RefyHysteresis:")
                temp_data.append(temp)           
                offset+=4

                if CustomRefID > 0:
                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("EntryStart:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("EntryStop:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("ExitStart:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("ExitStop:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<b', rawBytes, offset)[0]
                    temp_data.append("Contains:")
                    temp_data.append(temp)
                    offset+=1

                    result["Count"] = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp = result["Count"]
                    temp_data.append("Count:")
                    temp_data.append(temp) 
                    offset+=2
                    point_i = 0 
                    if (result["Count"] > 0) :
                        for i in range(result["Count"]):
                            temp_data.append("Point{0}X:".format(point_i))
                            temp_data.append(struct.unpack_from('<f', rawBytes, offset)[0])
                            offset+=4
                            temp_data.append("Point{0}Y:".format(point_i))
                            temp_data.append(struct.unpack_from('<f', rawBytes, offset)[0])
                            offset+=4
                            point_i +=1
                    temp = struct.unpack_from('<B', rawBytes, offset)[0]
                    temp_data.append("FunctionType:")
                    temp_data.append(temp)
                    offset+=1

                    result["FunctionParaCount"] = struct.unpack_from('<B', rawBytes, offset)[0]
                    temp = result["FunctionParaCount"]
                    temp_data.append("FunctionParaCount:")
                    temp_data.append(temp)
                    offset+=1
                    if (result["FunctionParaCount"] > 0) :
                        para_i = 0
                        for i in range(result["FunctionParaCount"]):
                            temp_data.append("FunctionPZara{0}".format(para_i))
                            temp_data.append(struct.unpack_from('<f', rawBytes, offset)[0])
                            offset+=4
                            para_i+=1
                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("RefPointX:")
                    temp_data.append(temp)
                    offset+=4

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("RefPointY:")
                    temp_data.append(temp)         
                    offset+=4
                ref_conf.append(temp_data) 
            temp_len = len(ref_conf)
            temp_data = []
            temp_data.append("")
            if temp_len < 20:
                for i in range(20-temp_len):
                    ref_conf.append(temp_data)
            print("结束offset",offset)
            if ((start_offset+data_len)!=offset):
                logging.error('read_ref_conf check data len error satrt_offset = {0} data_len = {1} offset = {2}'.format(start_offset,data_len,offset))
                raise("error")
            break
        else:
            offset+=data_len
            if RawBytesSize==offset:
                logging.error('read_ref_conf fail,没有获得需要的数据')
                raise Exception("没有获得需要的数据")
    return ref_conf

#0x06 变长数据
def read_EoConf(rawBytes,RawBytesSize,offset):
    while True:
        identity=struct.unpack_from('<B', rawBytes, offset)[0]
        offset+=1
        data_len=struct.unpack_from('<L', rawBytes, offset)[0]
        offset+=4
        if identity==0x06:
            start_offset = offset
            print("EoConf数据长度",data_len)
            print("起始offset",offset)
            result = {}
            eo_conf = []
            temp_data = []
            temp_data.append("EoConf")
            eo_conf.append(temp_data)
            result["RefCount"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("EoCount:")
            temp_data.append(temp)
            eo_conf.append(temp_data)
            offset+=1
            if (result["RefCount"] <1):
                temp_len = len(eo_conf)
                temp_data = []
                temp_data.append("")
                for i in range(20-temp_len):
                    eo_conf.append(temp_data)
                break
            j = 0
            for i in range(result["RefCount"] ):
                j += 1
                # result["Eo{0}index".format(j)] = struct.unpack_from('<H', rawBytes, offset)[0]
                temp_data = []
                temp = struct.unpack_from('<H', rawBytes, offset)[0]
                temp_data.append("Eo{0};".format(temp))
                # temp_data.append(temp)
                # eo_conf.append(temp_data)
                offset+=2

                # result["Eo{0}Type".format(j)] = struct.unpack_from('<B', rawBytes, offset)[0]
                eo_type = temp =  struct.unpack_from('<B', rawBytes, offset)[0]
                temp_data.append("Type:")
                temp_data.append(temp)
                offset+=1

                Reference_Identity = temp =  struct.unpack_from('<B', rawBytes, offset)[0]
                temp_data.append("Reference Identity:")
                temp_data.append(temp)
                offset+=1

                Use_Global_Hysteresis = temp =  struct.unpack_from('<B', rawBytes, offset)[0]
                temp_data.append("Use Global Hysteresis:")
                temp_data.append(temp)
                offset+=1


                # result["Eo{0}xHysteresis".format(j)] = struct.unpack_from('<f', rawBytes, offset)[0]
                temp =  struct.unpack_from('<f', rawBytes, offset)[0]
                temp_data.append("xHysteresis:")
                temp_data.append(temp)             
                offset+=4

                # result["Eo{0}yHysteresis".format(j)] = struct.unpack_from('<f', rawBytes, offset)[0]
                temp =  struct.unpack_from('<f', rawBytes, offset)[0]
                temp_data.append("yHysteresis:")
                temp_data.append(temp)
                offset+=4

                #新加start
                default =["Not Involved","Involved"]
                temp =  struct.unpack_from('B', rawBytes, offset)[0]
                temp_data.append("Involed evaluation:")
                temp_data.append(default[temp])
                offset+=1

                offset+=32
                #新加end

                # result["Eo{0}XRefID".format(j)] = struct.unpack_from('<B', rawBytes, offset)[0]
                # temp =  struct.unpack_from('<B', rawBytes, offset)[0]
                # temp_data.append("XRefID:")
                # temp_data.append(temp)
                # offset+=1

                # # result["Eo{0}YRefID".format(j)] = struct.unpack_from('<B', rawBytes, offset)[0]
                # temp =  struct.unpack_from('<B', rawBytes, offset)[0]
                # temp_data.append("YRefID:")
                # temp_data.append(temp)
                # offset+=1

                # result["Eo{0}index".format(j)] = struct.unpack_from('<L', rawBytes, offset)[0]
                eo_data_len = temp =  struct.unpack_from('<L', rawBytes, offset)[0]
                # temp_data.append("Eo{0}index".format(j))
                # temp_data.append(temp)
                offset+=4
                # print("需要解析数据长度：",temp)
                # print("offset:=",offset)

                # result["Eo{0}index".format(j)] = struct.unpack_from('<l', rawBytes, offset)[0]
                # temp =  struct.unpack_from('<L', rawBytes, offset)[0]
                # temp_data.append("Eo{0}index".format(j))
                # temp_data.append(temp)
                if ((eo_type == 0)):#line
                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("EntryStart:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("EntryStop:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("Point0X:")
                    temp_data.append(temp)
                    offset+=4

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("Point0Y:")
                    temp_data.append(temp)
                    offset+=4

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("Point1X:")
                    temp_data.append(temp)
                    offset+=4

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("Point1Y:")
                    temp_data.append(temp)
                    offset+=4


                elif (eo_type == 1):#PoloyGon
                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("EntryStart:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("EntryStop:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("ExitStart:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("ExitStop:")
                    temp_data.append(temp)
                    offset+=2

                    temp = struct.unpack_from('<b', rawBytes, offset)[0]
                    temp_data.append("Contains:")
                    temp_data.append(temp)
                    offset+=1

                    PoinstCount = temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("PoinstCount:")
                    temp_data.append(temp)
                    offset+=2
                    if PoinstCount > 0:
                        for i in range(PoinstCount):
                            temp = struct.unpack_from('<f', rawBytes, offset)[0]
                            temp_data.append("Point{0}X:".format(i))
                            temp_data.append(temp)
                            offset+=4

                            temp = struct.unpack_from('<f', rawBytes, offset)[0]
                            temp_data.append("Point{0}Y:".format(i))
                            temp_data.append(temp)
                            offset+=4

                    TunnelCount = temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    temp_data.append("TunnelCount:")
                    temp_data.append(temp)
                    offset+=2
                    if (TunnelCount > 0):
                        for i in range(TunnelCount):
                            j = i+1
                            temp = struct.unpack_from('<h', rawBytes, offset)[0]
                            temp_data.append("Tunnex{0}Start:".format(j))
                            temp_data.append(temp)
                            offset += 2
                            temp = struct.unpack_from('<h', rawBytes, offset)[0]
                            temp_data.append("Tunnex{0}End:".format(j))
                            temp_data.append(temp)
                            offset += 2
                    CalcCount = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                    temp_data.append("CalcCount:")
                    temp_data.append(temp)
                    offset += 1 
                    if (CalcCount >0):
                        CalcType_list = []
                        CalcType_list.append("NULL")
                        CalcType_list.append("AverageY")
                        CalcType_list.append("Time")
                        CalcType_list.append("Speed")
                        CalcType_list.append("Inflexion")
                        fun_list = []
                        fun_list.append("NULL:")
                        fun_list.append("MinAverageY:")
                        fun_list.append("MaxAverageY:")
                        fun_list.append("MinTime:")
                        fun_list.append("MaxTime:")
                        fun_list.append("MinSpeed:")
                        fun_list.append("MaxSpeed:")
                        fun_list.append("MinBending:")
                        fun_list.append("MaxBending:")
                        for i in range(CalcCount):
                            CalcType = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                            temp_data.append("CalcType:")
                            temp_data.append(CalcType_list[CalcType])
                            offset += 1

                            temp = struct.unpack_from('<f', rawBytes, offset)[0]
                            temp_data.append(fun_list[2*CalcType-1])
                            temp_data.append(temp)
                            offset += 4

                            temp = struct.unpack_from('<f', rawBytes, offset)[0]
                            temp_data.append(fun_list[2*CalcType])
                            temp_data.append(temp)
                            offset += 4
                            if (CalcType == 4):
                                temp = struct.unpack_from('<f', rawBytes, offset)[0]
                                temp_data.append("dx:")
                                temp_data.append(temp)
                                offset += 4

                elif (eo_type == 3):#envelope
                    offset += eo_data_len
                    # temp = struct.unpack_from('<B', rawBytes, offset)[0]
                    # temp_data.append("FunctionFlag:")
                    # temp_data.append(temp)
                    # offset += 1

                    # temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    # temp_data.append("FunctionFlag:")
                    # temp_data.append(temp)
                    # offset += 2
    
                    # UpLimitCount =  temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    # temp_data.append("UpLimitCount:")
                    # temp_data.append(temp)
                    # offset += 2

                    # DownLimitCount = temp = struct.unpack_from('<h', rawBytes, offset)[0]
                    # temp_data.append("DownLimitCount:")
                    # temp_data.append(temp)
                    # offset += 2

                    # if DownLimitCount > 0:
                    #     for i in range(DownLimitCount):
                    #         j = i +1
                    #         temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    #         temp_data.append("EnPoints{0}X:".format(j) )
                    #         temp_data.append(temp)
                    #         offset+=4

                    #         temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    #         temp_data.append("EnPoints{0}Y:".format(j) )
                    #         temp_data.append(temp)
                    #         offset+=4
                    pass

                elif (eo_type == 5):#Hysteresis
                    temp = struct.unpack_from('<B', rawBytes, offset)[0]
                    temp_data.append("HysteresisType:")
                    if temp > 0:
                        temp_data.append("X")
                        temp_p = "Y"
                        temp_d = "X"
                    else:
                        temp_data.append("Y")
                        temp_p = "X"
                        temp_d = "Y"
                    offset+=1

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("Position{0}:".format(temp_p) )
                    temp_data.append(temp)
                    offset+=4

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("MinValue{0}:".format(temp_d))
                    temp_data.append(temp)
                    offset+=4

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("MaxValue{0}:".format(temp_d))
                    temp_data.append(temp)
                    offset+=4

                elif(eo_type == 6):#Area
                    area_type = []
                    area_type.append("null")
                    area_type.append("Delttla_Y")
                    area_type.append("DIG-IN")
                    area_type.append("Gradient")
                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("MinCoordinates:")
                    temp_data.append(temp)
                    offset+=4

                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("MaxCoordinates:")
                    temp_data.append(temp)
                    offset+=4

                    area_type_temp = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                    temp_data.append("AreaCalcType:")
                    temp_data.append(area_type[area_type_temp])
                    offset+=1
                    if (area_type_temp == 1):#Deltla-Y
                        temp = struct.unpack_from('<f', rawBytes, offset)[0]
                        temp_data.append("MaxDeltaY:")
                        temp_data.append(temp)
                        offset+=4
                    elif(area_type_temp == 2):#DIG_IN
                        OperationType = []
                        OperationType.append("NULL")
                        OperationType.append("Level:")
                        OperationType.append("Edge:")
                        StateType = []
                        StateType.append("null")
                        StateType.append("null")
                        StateType.append("null")
                        StateType.append("High:")
                        StateType.append("Low:")
                        StateType.append("Rising:")
                        StateType.append("Faling:")
                        temp = struct.unpack_from('<H', rawBytes, offset)[0]
                        temp_data.append("SdoGroupIndex:")
                        temp_data.append(temp)
                        offset+=2

                        temp = struct.unpack_from('<B', rawBytes, offset)[0]
                        temp_data.append("SdoSubIndex:")
                        temp_data.append(temp)
                        offset+=1

                        temp_Operation =  temp = struct.unpack_from('<B', rawBytes, offset)[0]
                        temp_data.append("OperationType:")
                        temp_data.append(OperationType[temp])
                        offset+=1
                        
                        temp = struct.unpack_from('<B', rawBytes, offset)[0]
                        temp_data.append("StateType:")
                        temp_data.append(StateType[temp_Operation*2 +temp])
                        offset+=1
                    elif(area_type_temp == 3):#Gradient
                        GradientType = []
                        GradientType.append("X")
                        GradientType.append("Y")
                        g_type= []
                        g_type.append("Left")
                        g_type.append("Up")
                        g_type.append("Right")
                        g_type.append("Down")
                        temp = struct.unpack_from('<B', rawBytes, offset)[0]
                        temp_data.append("RradientType:")
                        temp_data.append(GradientType[temp])
                        offset+=1

                        temp = struct.unpack_from('<B', rawBytes, offset)[0]
                        temp_data.append("Entry:")
                        temp_data.append(g_type[temp])
                        offset+=1

                        temp = struct.unpack_from('<B', rawBytes, offset)[0]
                        temp_data.append("Exit:")
                        temp_data.append(g_type[temp])
                        offset+=1

                        temp = struct.unpack_from('<f', rawBytes, offset)[0]
                        temp_data.append("MinGradient:")
                        temp_data.append(temp)
                        offset+=4

                        temp = struct.unpack_from('<f', rawBytes, offset)[0]
                        temp_data.append("MaxGradient:")
                        temp_data.append(temp)
                        offset+=4

                # print("解析数据长度：",offset)
                FieldbusResultCount = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                temp_data.append("FieldbusResultCount:")
                temp_data.append(temp)
                offset+=1
                print(offset)
                if FieldbusResultCount > 0:
                    for i in range(FieldbusResultCount):
                        FieldbusResultName ,offset = readToZero(rawBytes, RawBytesSize, offset)
                        temp_data.append("FieldbusResultName:")
                        temp_data.append(FieldbusResultName)
                        
                        temp =  struct.unpack_from('<H', rawBytes, offset)[0]
                        temp_data.append("FieldbusResultGroupIndex:")
                        temp_data.append(temp)
                        offset+=2

                        temp =  struct.unpack_from('<B', rawBytes, offset)[0]
                        temp_data.append("FieldbusResultSubIndex:")
                        temp_data.append(temp)
                        offset+=1
                eo_conf.append(temp_data)

                if ((eo_type == 1) or (eo_type == 0)):#PoloyGon
                    EO_Fieldbus_Position_Count = struct.unpack_from('<B', rawBytes, offset)[0]
                    offset+=1
                    offset+=EO_Fieldbus_Position_Count*6

            print("结束offset",offset)
            if ( offset!=(start_offset+data_len)):
                logging.error('read_EoConf check data len error satrt_offset = {0} data_len = {1} offset = {2}'.format(start_offset,data_len,offset))
                print('eo_conf检查长度不配')
                raise('error')
            temp_len = len(eo_conf)
            temp_data = []
            temp_data.append("")
            if (temp_len < 20):
                for i in range(20-temp_len):
                    eo_conf.append(temp_data)
            break
        else:
            offset+=data_len
            if RawBytesSize==offset:
                logging.error('read_EoConf fail,没有获得需要的数据')
                raise Exception("没有获得需要的数据")
    return eo_conf    

#0x08
def read_ref_result(rawBytes,RawBytesSize,offset):
    while True:
        identity=struct.unpack_from('<B', rawBytes, offset)[0]
        offset+=1
        #logging.error("ref_result0")
        data_len=struct.unpack_from('<L', rawBytes, offset)[0]
        #logging.error("ref_result1")
        offset+=4
        if identity==0x08:
            print("ref_result数据长度",data_len)
            print("起始offset",offset)
            start_offset = offset
            result = {}
            ref_result =[]
            temp_data = []
            temp_data.append("RefResult")
            ref_result.append(temp_data)
            result["RefCount"] =struct.unpack_from('<B', rawBytes, offset)[0]
            #logging.error("ref_result2")
            temp_data = []
            temp =  result["RefCount"]#struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("RefCount:")
            temp_data.append(temp)
            ref_result.append(temp_data) 
            offset += 1
            if(result["RefCount"] > 0):
                for i in range(result["RefCount"]):
                    j = i+1
                    temp_data = []
                    # result["RefId{0}".format(j)] =struct.unpack_from('<H', rawBytes, offset)[0]
                    
                    temp_data.append("Ref{0}:".format(j))
                    temp =  struct.unpack_from('<B', rawBytes, offset)[0]
                    temp_data.append("RefId:")
                    temp_data.append(temp)
                    offset += 1
                    #logging.error("ref_result3")
                    # result["bFindValue{0}".format(j)] = data_len=struct.unpack_from('<B', rawBytes, offset)[0]

                    temp =  struct.unpack_from('<B', rawBytes, offset)[0]
                    temp_data.append("bFindValue")
                    temp_data.append(temp)
                    offset += 1

                    # result["PointX{0}".format(j)] = data_len=struct.unpack_from('<f', rawBytes, offset)[0]
                    temp =  struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("PointX")
                    temp_data.append(temp)
                    offset += 4

                    # result["PointY{0}".format(j)] = data_len=struct.unpack_from('<f', rawBytes, offset)[0]
                    temp =  struct.unpack_from('<f', rawBytes, offset)[0]
                    temp_data.append("PointY")
                    temp_data.append(temp)
                    ref_result.append(temp_data) 
                    offset += 4
            print("结束offset",offset)
            if ((start_offset+data_len)!=offset):
                logging.error('read_ref_result check data len error satrt_offset = {0} data_len = {1} offset = {2}'.format(start_offset,data_len,offset))
                raise("error")
            temp_len = len(ref_result)
            temp_data = []
            temp_data.append("")
            if temp_len < 20:
                for i in range(20 -temp_len):
                    ref_result.append(temp_data) 
            break
        else:
            offset+=data_len
            #logging.error("ref_result4")
            if RawBytesSize==offset:
                logging.error('read_ref_result fail,RawBytesSize = {0},offset = {1},没有获得需要的数据'.format(RawBytesSize,offset))
                raise Exception("没有获得需要的数据")
    return ref_result

#0x10 变长数据
def read_eo_result(rawBytes,RawBytesSize,offset):
    while True:
        identity=struct.unpack_from('<B', rawBytes, offset)[0]
        offset+=1
        data_len=struct.unpack_from('<L', rawBytes, offset)[0]
        offset+=4
        if identity==0x10:
            print("eo_data_len:data_len",data_len)
            print("eo_data_offset",offset)
            start_offset = offset
            eo_list = []
            eo_list.append("XMax-X.")
            eo_list.append("XMax-Y.")
            eo_list.append("YMax-X.")
            eo_list.append("YMax-Y.")
            eo_list.append("XMin-X.")
            eo_list.append("XMin-Y.")
            eo_list.append("YMin-X.")
            eo_list.append("YMin-Y.")
            eo_list.append("Entry-X.")
            eo_list.append("Entry-Y.")
            eo_list.append("Exit-X.")
            eo_list.append("Exit-Y.")
            eo_list.append("PeakValley_Y.")

            
            error_list = []
            error_list.append("ENTRY")
            error_list.append("EXIT")
            error_list.append("PointExceedHY")
            error_list.append("XRefNotFind")
            error_list.append("YRefNotFind")
            error_list.append("OnlineNotCalc")
            error_list.append("RepeatPass")
            error_list.append("Reserved")
            error_list.append("AverageY")
            error_list.append("TIME")
            error_list.append("SPEED")
            error_list.append("Inflexion")
            error_list.append("Break")
            error_list.append("Reserved")
            error_list.append("Reserved")
            error_list.append("Reserved")
            error_list.append("DeltlaY")
            error_list.append("DigIn")
            error_list.append("Geadient")
            error_list.append("Reserved")
            error_list.append("Reserved")
            error_list.append("Reserved")
            error_list.append("Reserved")
            error_list.append("Reserved")
            error_list.append("HysteresisX")
            error_list.append("HysteresisY")

            result = {}
            eo_result = []
            temp_data = []
            temp_data.append("EoResult")
            eo_result.append(temp_data)
            # result["CraftResult"] = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data = []
            temp =  struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("OK/NG")
            temp_data.append(temp)
            eo_result.append(temp_data) 
            offset +=1

            result["CurveCount"] = struct.unpack_from('<B', rawBytes, offset)[0]
            offset +=1
            if(result["CurveCount"] > 0):###
                for i in range(result["CurveCount"]):
                    j = i+1
                    # result["Curve{0}ID".format(j)] = struct.unpack_from('<B', rawBytes, offset)[0]
                    temp_data = []
                    temp = struct.unpack_from('<B', rawBytes, offset)[0]
                    # temp_data.append("CurveID:")
                    # temp_data.append(temp)
                    offset += 1

                    # result["Curve{0}Result".format(j)] = struct.unpack_from('<B', rawBytes, offset)[0]
                    temp = struct.unpack_from('<B', rawBytes, offset)[0]
                    # temp_data.append("CurveResult:")
                    # temp_data.append(temp)
                    # eo_result.append(temp_data) 
                    offset += 1

                    result["EoCount"] = struct.unpack_from('<B', rawBytes, offset)[0]
                    temp = result["EoCount"]#struct.unpack_from('<B', rawBytes, offset)[0]
                    temp_data.append("EoCount:")
                    temp_data.append(temp)
                    eo_result.append(temp_data) 
                    offset += 1
                    if (result["EoCount"] > 0):
                        for i in range(result["EoCount"]):
                            j = i + 1
                            temp_data = []
                            temp = struct.unpack_from('<B', rawBytes, offset)[0]
                            temp_data.append("Eo{0}:".format(temp))
                            # result["EoIndex{0}".format(j)] = struct.unpack_from('<B', rawBytes, offset)[0]
                            offset += 1
                            # result["EoType{0}".format(j)] = struct.unpack_from('<B', rawBytes, offset)[0]
                            eo_type = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                            temp_data.append("Type:")
                            temp_data.append(temp)
                            offset += 1
                            # result["EoResult{0}".format(j)] = struct.unpack_from('<B', rawBytes, offset)[0]
                            temp = struct.unpack_from('<B', rawBytes, offset)[0]
                            temp_data.append("Result:")
                            temp_data.append(temp)
                            offset += 1
                            error_code = temp =struct.unpack_from('<Q', rawBytes, offset)[0]
                            offset +=8
                            print(hex(error_code))
                            if error_code > 0:
                                temp_data.append("Error Information :")
                                for i in range(64):
                                    if (( error_code >> i )&0x0001):
                                        temp_data.append(error_list[i])
                            else:
                                temp_data.append("Error Information :")
                                temp_data.append(" No Error ")
                            result_code_mask = temp = struct.unpack_from('<Q', rawBytes, offset)[0]
                            if (eo_type == 1):
                                result_code_mask = 0x1FFF
                            elif (eo_type == 0):
                                result_code_mask = 0x0300
                            elif(eo_type == 3):
                                result_code_mask = 0x1FFF
                            elif (eo_type == 5):
                                result_code_mask = 0x0F11
                            elif (eo_type == 6):
                                result_code_mask = 0
                                # result_code_mask = 0x0F88
                                # result_code_mask = 0x0F11
                            offset +=8
                            # print("解析完code_maskoffset",offset)
                            if  True:
                                for i in range(64):
                                    if (( result_code_mask >> i )&0x0001):
                                        varisOk = struct.unpack_from('<B', rawBytes, offset)[0]
                                        offset+=1
                                        RealVar = struct.unpack_from('<f', rawBytes, offset)[0]
                                        offset +=4
                                        MinSet = struct.unpack_from('<f', rawBytes, offset)[0]
                                        offset +=4
                                        MaxSet = struct.unpack_from('<f', rawBytes, offset)[0]
                                        offset +=4
                                        temp_data.append("{0}VarIsOK:".format(eo_list[i]))
                                        temp_data.append(varisOk)
                                        temp_data.append("{0}RealVar:".format(eo_list[i]))
                                        temp_data.append(RealVar)
                                        temp_data.append("{0}MinSet:".format(eo_list[i]))
                                        temp_data.append(MinSet)
                                        temp_data.append("{0}MaxSet:".format(eo_list[i]))
                                        temp_data.append(MaxSet)
                                if ((eo_type == 0)):#line
                                    pass
                                elif (eo_type == 3):
                                    pass
                                elif (eo_type == 1):#PoloyGon

                                    CalcCount = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                                    temp_data.append("CalcCount:")
                                    temp_data.append(temp)
                                    offset += 1 

                                    if (CalcCount >0):
                                        CalcType_list = []
                                        CalcType_list.append("NULL")
                                        CalcType_list.append("AverageY")
                                        CalcType_list.append("Time")
                                        CalcType_list.append("Speed")
                                        CalcType_list.append("Inflexion")
                                        fun_list = []
                                        fun_list.append("MinResultItem:")
                                        fun_list.append("MaxResultItem:")
                                        fun_list.append("inflexCoord_X:")
                                        fun_list.append("inflexCoord_Y:")
                                        for i in range(CalcCount):
                                            CalcType = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                                            temp_data.append("CalcType:")
                                            temp_data.append(CalcType_list[CalcType])
                                            offset += 1
                                            if (CalcType == 1):
                                                temp_data.append("ResultItem:")
                                                temp_i = 1
                                            if (CalcType == 2):
                                                temp_i = 2
                                            if (CalcType == 3):
                                                temp_i = 2
                                            if (CalcType == 4):
                                                temp_i = 4   
                                            for j in range(temp_i): 
                                                if CalcType > 1:
                                                   temp_data.append(fun_list[j])
                                                VarisOk =  struct.unpack_from('<B', rawBytes, offset)[0]
                                                # temp_data.append("VarIsOk:{0}".format(VarisOk))
                                                temp_data.append("VarIsOk:")
                                                temp_data.append(VarisOk)
                                                offset += 1
                                                RealVar = struct.unpack_from('<f', rawBytes, offset)[0]
                                                # temp_data.append("RealVar:, {0}".format(RealVar))
                                                temp_data.append("RealVar:")
                                                temp_data.append(RealVar)
                                                offset +=4
                                                MinSet = struct.unpack_from('<f', rawBytes, offset)[0]
                                                # temp_data.append("MinSet:, {0}".format(MinSet))
                                                temp_data.append("MinSet:")
                                                temp_data.append(MinSet)
                                                offset +=4
                                                MaxSet = struct.unpack_from('<f', rawBytes, offset)[0]
                                                # temp_data.append("MaxSet:, {0}".format(MaxSet))
                                                temp_data.append("MaxSet:")
                                                temp_data.append(MaxSet)
                                                offset +=4

                                elif (eo_type == 5):#Hysteresis
                                    temp = struct.unpack_from('<B', rawBytes, offset)[0]
                                    temp_data.append("HysteresisType:")
                                    if temp > 0:
                                        temp_data.append("X")
                                        temp_p = "Y"
                                        temp_d = "X"
                                    else:
                                        temp_data.append("Y")
                                        temp_p = "X"
                                        temp_d = "Y"
                                    offset+=1

                                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                                    temp_data.append("Position{0}:".format(temp_p) )
                                    temp_data.append(temp)
                                    offset+=4

                                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                                    temp_data.append("MinValue{0}:".format(temp_d))
                                    temp_data.append(temp)
                                    offset+=4

                                    temp = struct.unpack_from('<f', rawBytes, offset)[0]
                                    temp_data.append("MaxValue{0}:".format(temp_d))
                                    temp_data.append(temp)
                                    offset+=4
                                elif(eo_type == 6):#Area
                                    area_type = []
                                    area_type.append("null")
                                    area_type.append("Delttla_Y")
                                    area_type.append("DIG-IN")
                                    area_type.append("Gradient")
                                    # temp = struct.unpack_from('<f', rawBytes, offset)[0]
                                    # temp_data.append("MinCoordinates:")
                                    # temp_data.append(temp)
                                    # offset+=4

                                    # temp = struct.unpack_from('<f', rawBytes, offset)[0]
                                    # temp_data.append("MaxCoordinates:")
                                    # temp_data.append(temp)
                                    # offset+=4
                                    # offset+=1
                                    offset+=91
                                    area_type_temp = temp = struct.unpack_from('<B', rawBytes, offset)[0]
                                    temp_data.append("AreaCalcType:")
                                    temp_data.append(area_type[area_type_temp])
                                    offset+=1
                                    if (area_type_temp == 1):#Deltla-Y
                                        temp_Deltla_Y = ["Min","Max","Entry2-X","Extry2-Y","Exit2-X","Exit2-Y"]
                                        for temp_name in temp_Deltla_Y:
                                            offset, ResultItem  = read_Resultltem(rawBytes, offset)
                                            for tamp_result in ResultItem :
                                                temp_data.append("{0}_{1}:".format(temp_name,tamp_result))
                                                temp_data.append(ResultItem["{0}".format(tamp_result)])
                                    elif(area_type_temp == 2): #DIG_IN
                                        offset , ResultItem = read_Resultltem(rawBytes, offset)
                                        for tamp_result in ResultItem :
                                            temp_data.append("{0}_{1}:".format("ResultItem",tamp_result))
                                            temp_data.append(ResultItem["{0}".format(tamp_result)])
                                    elif(area_type_temp == 3):#Gradient
                                        for i in range(3):
                                            temp = struct.unpack_from('<f', rawBytes, offset)[0]
                                            temp_data.append("Gradient{0}X:".format(i))
                                            temp_data.append(temp)
                                            offset+=4

                                            temp = struct.unpack_from('<f', rawBytes, offset)[0]
                                            temp_data.append("Gradient{0}Y:".format(i))
                                            temp_data.append(temp)
                                            offset+=4
                                        temp_GradientX = ["Min ResultItem","Max ResultItem"]
                                        for temp_name in temp_GradientX:
                                            offset, ResultItem = read_Resultltem(rawBytes, offset)
                                            for tamp_result in ResultItem :
                                                temp_data.append("{0}_{1}:".format(temp_name,tamp_result))
                                                temp_data.append(ResultItem["{0}".format(tamp_result)])
                                elif(eo_type == 7):#EOCalc
                                    # 读取 EOCalcVarCount (U8)
                                    eo_calc_var_count = struct.unpack_from('<B', rawBytes, offset)[0]
                                    temp_data.append("EOCalcVarCount:")
                                    temp_data.append(eo_calc_var_count)
                                    offset += 1

                                    # 读取 EOCalcResult 数组
                                    if eo_calc_var_count > 0:
                                        for i in range(eo_calc_var_count):
                                            # 读取每个 EOResultItem
                                            offset, result_item = read_Resultltem(rawBytes, offset)

                                            # 添加结果项数据
                                            for result_key in result_item:
                                                temp_data.append("EOCalcResult{0}_{1}:".format(i+1, result_key))
                                                temp_data.append(result_item[result_key])
                                eo_result.append(temp_data)

            # print("解析完eo数据offset = ",offset)
            if ((start_offset+data_len)!=offset):
                logging.error('read_eo_result check data len error satrt_offset = {0} data_len = {1} offset = {2}'.format(start_offset,data_len,offset))
            temp_len = len(eo_result)
            temp_data = []
            temp_data.append("")
            if temp_len < 20:
                for i in range(20-temp_len):
                    eo_result.append(temp_data) 
            break
        else:
            offset+=data_len
            if RawBytesSize==offset:
                logging.error('read_eo_result fail,没有获得需要的数据')
                raise Exception("没有获得需要的数据")
    return eo_result

def read_python_data(rawBytes,RawBytesSize,offset):
    while True:
        identity=struct.unpack_from('<B', rawBytes, offset)[0]
        offset+=1
        data_len=struct.unpack_from('<L', rawBytes, offset)[0]
        offset+=4
        if identity==0x20:
            result={}
            python_data = []
            temp_data = []
            temp_data.append("Curve Result")
            python_data.append(temp_data)
            result['is_pass']=struct.unpack_from('<?', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("is_pass")
            temp_data.append(result['is_pass'])
            # python_data.append(temp_data)
            offset+=1

            result['Operator'],offset=readToZero(rawBytes,RawBytesSize,offset)
            temp_data = []
            temp_data.append("Operator")
            temp_data.append(result['Operator'])
            # python_data.append(temp_data)
            result['time_min_t']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve TimeMin_T")
            temp_data.append(result['time_min_t'])
            python_data.append(temp_data)
            offset+=4

            result['time_min_x']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve TimeMin_X:")
            temp_data.append(result['time_min_x'])
            python_data.append(temp_data)
            offset+=4

            result['time_min_force']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve TimeMin_Force:")
            temp_data.append(result['time_min_force'])
            python_data.append(temp_data)
            offset+=4

            result['time_max_t']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve TimeMax_T:")
            temp_data.append(result['time_max_t'])
            python_data.append(temp_data)
            offset+=4

            result['time_max_x']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve TimeMax_X:")
            temp_data.append(result['time_max_x'])
            python_data.append(temp_data)
            offset+=4

            result['time_max_force']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve TimeMax_Force:")
            temp_data.append(result['time_max_force'])
            python_data.append(temp_data)
            offset+=4

            result['time_average']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve TimeAverage:")
            temp_data.append(result['time_average'])
            python_data.append(temp_data)
            offset+=4

            result['x_min_t']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve Xmin_T:")
            temp_data.append(result['x_min_t'])
            python_data.append(temp_data)
            offset+=4

            result['x_min_x']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve Xmin_X:")
            temp_data.append(result['x_min_x'])
            python_data.append(temp_data)
            offset+=4

            result['x_min_force']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve Xmin_Force:")
            temp_data.append(result['x_min_force'])
            python_data.append(temp_data)
            offset+=4

            result['x_max_t']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve Xmax_T:")
            temp_data.append(result['x_max_t'])
            python_data.append(temp_data)
            offset+=4

            result['x_max_x']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve Xmax_X:")
            temp_data.append(result['x_max_x'])
            python_data.append(temp_data)
            offset+=4

            result['x_max_force']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve Xmax_Force:")
            temp_data.append(result['x_max_force'])
            python_data.append(temp_data)
            offset+=4

            result['x_average']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve XAverage:")
            temp_data.append(result['x_average'])
            python_data.append(temp_data)
            offset+=4

            result['force_min_t']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve ForceMin_T:")
            temp_data.append(result['force_min_t'])
            python_data.append(temp_data)
            offset+=4

            result['force_min_x']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve ForceMin_X:")
            temp_data.append(result['force_min_x'])
            python_data.append(temp_data)
            offset+=4

            result['force_min_force']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve ForceMin_Force:")
            temp_data.append(result['force_min_force'])
            python_data.append(temp_data)
            offset+=4

            result['force_max_t']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve ForceMax_T:")
            temp_data.append(result['force_max_t'])
            python_data.append(temp_data)
            offset+=4

            result['force_max_x']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve ForceMax_X:")
            temp_data.append(result['force_max_x'])
            python_data.append(temp_data)
            offset+=4

            result['force_max_force']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve ForceMax_Force:")
            temp_data.append(result['force_max_force'])
            python_data.append(temp_data)
            offset+=4

            result['force_average']=struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data = []
            temp_data.append("Curve ForceAverage:")
            temp_data.append(result['force_average'])
            python_data.append(temp_data)
            temp_len =len(python_data)
            if temp_len < 30:
                for i in range (30- temp_len):
                    temp_data = []
                    temp_data.append("")
                    python_data.append(temp_data)
            break
        else:
            offset+=data_len
            if RawBytesSize==offset:
                logging.error('read_python_data fail,没有获得需要的数据')
                raise Exception("没有获得需要的数据")
    return result,python_data
#解析log文件


def write_json(datas_dict,name):
    try:
        with open(name,"w") as f:
            write_data=json.dumps(datas_dict)
            f.write(write_data)
    except:
        # print('写cdv-infro失败 name:{0}'.format(name))
        pass

def write_csv(name,datas) :
    global g_infrom_path,g_csv_infrom
    temp_data = []
    temp_data.append("")
    temp_data.append("")
    success = False
    try :
        with open(name, 'w', encoding='utf-8', newline='') as f:

            writer = csv.writer(f)
            for data in datas:
                writer.writerows(data)
                # writer.writerows(temp_data)   
            #print('write csv success')
            success = True
    except :
        logging.error(' creat csv except : {0} '.format(traceback.format_exc()))
        print('write csv fail')
    return success
    # if success:
    #     write_json(g_csv_infrom,g_infrom_path)


def wrte_csv_pd(name,datas):
    for data in datas :
        print(data)
        dataframe = pd.DataFrame(data)
        dataframe.to_csv(name,index=False,sep=',')


def openResultlogFile(fullName):
    error_flag = 0
    # result_solve_success = False
    global g_srcfile,g_dstfile,csv_path,g_csv_infrom,g_infrom_path,infrom_path,mainboard_id
    g_csv_infrom = {}
    if os.path.exists(fullName) and os.path.isfile(fullName):
        MaxSize = os.path.getsize(fullName)
        #print(fullName, ' have opened. File size: ',MaxSize)
        for slove_i in range(3):
            try:
                error_flag = 1
                with open(fullName, 'rb') as f:
                    data = f.read()
                data_len = len(data)
                if MaxSize!=data_len:
                    logging.error('log文件长度不匹配')
                    MaxSize = data_len
                offset = 0
                error_flag = 2
                jsonString = ''
                file_header = []
                temp_data = []
                csv_data = []
                temp_data.append("Overview Information")
                file_header.append(temp_data)
                FileType,offset = readToZero(data, MaxSize, offset)
                # file_header["FileType"] = FileType
                temp_data = []
                temp_data.append("FileType:")
                temp_data.append(FileType)
                file_header.append(temp_data)
                #print('FileType',FileType)
                DeviceName, offset = readToZero(data, MaxSize, offset)
                # file_header["DeviceName"] = DeviceName
                temp_data = []
                temp_data.append("DeviceName:")
                temp_data.append(DeviceName)
                file_header.append(temp_data)
                #print('DeviceName',DeviceName)

                sw_version,offset=readToZero(data, MaxSize, offset)
                # file_header["sw_version"] = sw_version
                temp_data = []
                temp_data.append("sw_version:")
                temp_data.append(sw_version)
                file_header.append(temp_data)
                #print('sw_version',sw_version)
                hd_version,offset=readToZero(data, MaxSize, offset)
                # file_header["hd_version"] = hd_version
                temp_data = []
                temp_data.append("hd_version:")
                temp_data.append(hd_version)
                file_header.append(temp_data)
                #print('hd_version',hd_version)
                config_file_version,offset=readToZero(data, MaxSize, offset)
                # file_header["config_file_version"] = config_file_version
                temp_data = []
                temp_data.append("config_file_version:")
                temp_data.append(config_file_version)
                file_header.append(temp_data)

                #print('config_file_version',config_file_version)
                sn, offset = readToZero(data, MaxSize, offset)
                # file_header["sn"] = sn
                temp_data = []
                temp_data.append("PartID:")
                temp_data.append(sn)
                file_header.append(temp_data)


                #print('sn',sn)
                nc_id,offset=readToZero(data, MaxSize, offset)
                # file_header["nc_id"] = nc_id
                temp_data = []
                temp_data.append("NcID:")
                temp_data.append(nc_id)
                file_header.append(temp_data)

                #print('nc_id',nc_id)
                nc_ip,offset=readToZero(data, MaxSize, offset)
                # file_header["nc_ip"] = nc_ip
                temp_data = []
                temp_data.append("NcIP:")
                temp_data.append(nc_ip)
                file_header.append(temp_data)

                #print('nc_ip',nc_ip)
                nc_model,offset=readToZero(data, MaxSize, offset)
                # file_header["nc_model"] = nc_model
                temp_data = []
                temp_data.append("NcModel:")
                temp_data.append(nc_model)
                file_header.append(temp_data)
                error_flag = 3
                #print('nc_model',nc_model)
                timestamp, offset = readToZero(data, MaxSize, offset)
                # file_header["timestamp"] = timestamp
                temp_data = []
                temp_data.append("TimeStamp:")
                temp_data.append(timestamp)
                file_header.append(temp_data)
                temp_len = len(file_header) 
                if(temp_len< 19):
                    for i in  range(19 -temp_len):
                        temp_data = []
                        temp_data.append("")
                        file_header.append(temp_data)
                try:
                    timestamp=datetime.datetime.strptime(timestamp, '%Y-%m-%d-%H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')
                except:
                    #timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    logging.error('获取log文件的timestamp数据出错')
                #print('binaryDataLen',struct.unpack_from('<L', data, offset))
                offset += 4
                #print('sn','timestamp', sn, timestamp)
                #print('identity',struct.unpack_from('<B', data, offset))
                error_flag = 4
                for i in range(3):
                    slove_success = False 
                    try:
                        curve_result = read_curve_data(data, MaxSize, offset)
                        slove_success = True
                    except:
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        logging.error('解析曲线数据出错')
                        raise("error")
                    time.sleep(0.5)
                # print(result)
                error_flag = 5
                for i in range(3):
                    slove_success = False 
                    try:
                        ref_result = read_ref_result(data, MaxSize, offset)
                        slove_success = True
                    except:    
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        logging.error('解析 ref_result 出错')
                        raise("error")
                    time.sleep(0.5)
                
                # print(result)
                error_flag = 6
                for i in range(3):
                    slove_success = False 
                    try:
                        ref_conf = read_ref_conf(data, MaxSize, offset)
                        slove_success = True
                    except:
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        logging.error('解析ref_conf出错')
                        raise("error")
                    time.sleep(0.5)
                # print(result)
                error_flag = 7
                for i in range(3):
                    slove_success = False 
                    try:
                        eo_conf = read_EoConf(data, MaxSize, offset)
                        slove_success = True
                    except:
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        logging.error('解析eo_conf出错')
                        raise("error")
                    time.sleep(0.5)
                error_flag = 8
                for i in range(3):
                    slove_success = False 
                    try:
                        eo_result = read_eo_result(data, MaxSize, offset)
                        slove_success = True
                    except:                        
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        logging.error('解析eo_result出错')
                        raise("error")
                    time.sleep(0.5)           
                # print(result)
                try:
                    error_flag = 9
                    result,python_result =read_python_data(data, MaxSize, offset)
                    # print(result)
                    csv_data.append(file_header)
                    csv_data.append(python_result)
                    csv_data.append(ref_conf)
                    csv_data.append(eo_conf)
                    csv_data.append(ref_result)
                    csv_data.append(eo_result)
                    csv_data.append(curve_result)
                    # csv_name = os.path.join(csv_path,)
                    # write_csv("test.csv",csv_data)
                    error_flag = 10
                    path_name , file_name = os.path.split(fullName)
                    (filename, extension) = os.path.splitext(file_name)
                    csv_name_tmp0 = os.path.join(csv_path,filename+'.csv')
                    csv_name_tmp1= check_and_rename(csv_name_tmp0,filename)
                    csv_name = os.path.join(csv_path,csv_name_tmp1+'.csv')
                    g_infrom_path = os.path.join(infrom_path,filename+'.json')
                    g_csv_infrom['is_pass']=result['is_pass']
                    g_csv_infrom['DeviceName']=DeviceName
                    g_csv_infrom['TimeStamp']=timestamp
                    g_csv_infrom['nc_id']=nc_id
                    g_csv_infrom['csv_name'] = csv_name_tmp1 + '.csv'
                    #g_csv_infrom['profile_name'] = get_prof_dic['conf']['profile']['name']
                    creat_csv_file(csv_name,csv_data)
                    error_flag = 11
                except:
                    #result={}
                    logging.error('read_python_data():fail {0},errorflag = {1}'.format(traceback.format_exc(),error_flag))
                offset += 28
                sampleCount, = struct.unpack_from('<L', data, offset)
                offset += 4
                #offset += 4
                '''for i in range(0, sampleCount):
                    t,x,y = struct.unpack_from('<3f', data, offset)
                    offset += 12
                    str = '[{0},{1},{2}],'.format(t, x, y)'''
                #print('total lenght: ', len(jsonString))
                return {'sn'                        :sn,
                        'TimeStamp'                 :timestamp,
                        'Operator'                  :result['Operator'],
                        'RawWaveformByteOffset'     :offset,
                        'SampleCount'               :sampleCount,
                        'ResultList'                :'',

                        'is_pass'                   :result['is_pass'],
                        'Operator'                  :result['Operator'],
                        'time_min_t'                :result['time_min_t'],
                        'time_min_x'                :result['time_min_x'],
                        'time_min_force'            :result['time_min_force'],
                        'time_max_t'                :result['time_max_t'],
                        'time_max_x'                :result['time_max_x'],
                        'time_max_force'            :result['time_max_force'],
                        'time_average'              :result['time_average'],
                        'x_min_t'                   :result['x_min_t'],
                        'x_min_x'                   :result['x_min_x'],
                        'x_min_force'               :result['x_min_force'],
                        'x_max_t'                   :result['x_max_t'],
                        'x_max_x'                   :result['x_max_x'],
                        'x_max_force'               :result['x_max_force'],
                        'x_average'                 :result['x_average'],
                        'force_min_t'               :result['force_min_t'],
                        'force_min_x'               :result['force_min_x'],
                        'force_min_force'           :result['force_min_force'],
                        'force_max_t'               :result['force_max_t'],
                        'force_max_x'               :result['force_max_x'],
                        'force_max_force'           :result['force_max_force'],
                        'force_average'             :result['force_average'],

                        'DeviceName'                :DeviceName,
                        'config_file_version'       :config_file_version,
                        'sw_version'                :sw_version,
                        'hd_version'                :hd_version,
                        'nc_id'                     :nc_id,
                        'nc_ip'                     :nc_ip,
                        'nc_model'                  :nc_model
                        }
            except:
                if (slove_i == 2):
                    logging.error('openResultlogFile fail,解析{0}文件出错，errorflag = {1},datalen = {2}'.format(fullName,error_flag,data_len))
                    g_srcfile=fullName
                    fpath,fname=os.path.split(g_srcfile)
                    shutil.move(g_srcfile,os.path.join(g_dstfile,fname))
                else:
                    time.sleep(1)
    else:
        logging.error('openResultlogFile fail,{0}不存或不是文件'.format(fullName))


conf_file =r'C:\Apache24\Django\configuration\file_server.conf'
creat_csv = False 
max_csv_number = 5000
csv_count = 0
get_csv_count =True


def web_get_conf():
    global is_active,user,password,path,ip,conf_change
    success=False
    try:
        r = requests.get("http://127.0.0.1/fileserver")
        returnDict = json.loads(r.text)
        if returnDict['activate'].lower()=='true':
            is_active=True
        else :
            is_active=False
        user = returnDict['user']
        password = returnDict['password']
        path = returnDict['path']
        ip = returnDict['ip']
        success =True
    except:
        pass
    return success
def get_csv_conf():
    success=False
    is_active=False
    try:
        r = requests.get("http://127.0.0.1/fileserver")
        returnDict = json.loads(r.text)
        if returnDict['activate'].lower()=='true':
            is_active=True
        else :
            is_active=False
        success =True
    except:
        pass
    if success==False:
        with open(conf_file, 'r') as f:
            data=f.read()
        data=json.loads(data)
        if data['file_server_is_activate'].lower()=='true':
            is_active=True
        else :
            is_active=False

    return is_active


def creat_csv_file(csv_name,csv_data):
    global conf_file,creat_csv,max_csv_number,csv_count,get_csv_count,creat_csv_is_error,csv_path,g_csv_infrom
    success = write_csv(csv_name,csv_data)
    if(success):
        csv_data_deal.creat_session()
        csv_data_deal.creat_table()
        success, count = csv_data_deal.data_count()
        g_csv_infrom['id'] = csv_data_deal.get_new_id()
        stringDate = g_csv_infrom['TimeStamp']
        g_csv_infrom['TimeStamp'] = datetime.datetime.fromtimestamp(time.mktime(time.strptime(stringDate,"%Y-%m-%d %H:%M:%S")))
        success = csv_data_deal.add_data(g_csv_infrom)
        if(success ):
            if (count >= max_csv_number):
                data = csv_data_deal.get_first()
                if(data):
                    try:
                        temp_path = os.path.join(csv_path,data.csv_name)
                        if(os.path.exists(temp_path)):
                            try:
                                os.remove(temp_path)
                                csv_data_deal.data_delete(data.id)
                            except:
                                print("delete csv: {0} fail".format(data.csv_name))
                        else:
                            csv_data_deal.data_delete(data.id)
                    except:
                        pass
                if(success == False):
                    logging.error('first_delete error')
        else:
            logging.error('csv infrom write Datebase error')
        csv_data_deal.close()
    else:
        logging.error('name : {0} write csv file error'.format(csv_name))


    # is_creat_csv = False
    # try:
    #     # write_csv(csv_name,csv_data)
    #     if get_csv_conf():
    #         if os.path.exists(r"z:"):
    #             creat_csv =True
    #             get_csv_count = True
    #             write_csv(csv_name,csv_data)
    #         else:
    #             if get_csv_count:
    #                 get_csv_count =False
    #                 files = os.listdir(csv_path)
    #                 csv_count = len(files)
    #             if (csv_count < max_csv_number) :
    #                 write_csv(csv_name,csv_data)
    #                 csv_count+=1
    #             else:
    #                 creat_csv_is_error = True
    # except:
    #     pass

def get_g_state1():
    global g_state1
    return g_state1

def get_csv_error():
    global creat_csv_is_error
    return creat_csv_is_error


def clear_csv_error():
    global creat_csv_is_error
    creat_csv_is_error = False



def check_dir(path):
    # print('检测所需环境')
    if (os.path.exists(path)):
        pass
    else:
        # print("文件夹{0}不存在   创建文件夹".format(path))
        os.makedirs(path)

def log_fail_deal(dic):
    success = False
    print('处理失败log')
    try:
        log_name = dic['log']
        src = os.path.join(Observer_DIR,log_name)
        file_time = datetime.datetime.now()
        str_time = file_time.strftime('%Y-%m-%d-%H-%M-%S')
        dir_path =  os.path.join(g_dstfile,str_time)
        check_dir(dir_path)
        dst = os.path.join(dir_path,log_name)
        shutil.move(src, dst)
        for item, fileName in Sys_Config_File_Dict.items():
            temp_path = os.path.join(dir_path,fileName)
            try:
                with open(temp_path,'wb') as f:
                    f.write(dic["conf"][fileName])
            except:
                pass
        if(dic['conf']['profile']['name']):
            temp_path = os.path.join(dir_path,dic['conf']['profile']['name'])
            with open(temp_path,'wb') as f:
                f.write(dic['conf']['profile']['data'])
        success = True
        if(dic['conf']['md5']):
            temp_path = os.path.join(dir_path,'md5')
            with open(temp_path,'w') as f:
                f.write(dic['conf']['md5'])
    except:
        logging.error('log_fail_deal():{0}'.format(traceback.format_exc()))
    return success
    






def add_fold_and_upload(dic):
    logFileName = dic['log']
    global upload_file_success
    # time.sleep(2)
    error_flag = 0
    tmpResultFilePath = os.path.join(BASE_TMP_DIR, 'Result')    
    oldname=os.path.join(tmpResultFilePath,logFileName)
    newname=os.path.join(BASE_DIR, 'Result',logFileName)  
    error_flag = 1
    uuids = check_config_profile_md5(dic['conf'])
    error_flag = 2
    if uuids :
        succeed=check_upload_product(dic, uuids)
        error_flag = 3
        # succeed = True
        if succeed :
            upload_file_success=True
            try :
                os.remove(oldname)
            except :
                logging.error(traceback.format_exc())
                #print('移动log失败')
                logging.error('移动{0}失败'.format(oldname))
        else:
            #print('upload_product file')
            upload_file_success=False
            log_fail_deal(dic)
            logging.error('upload_product file,errorflag = {0}'.format(error_flag))
    else:
        log_fail_deal(dic)



def add_all_file_upload():
    try:
        file_names=os.listdir(Observer_DIR)
        if(len(file_names) >0):
            conf = read_conf(g_confdir)
        for name in file_names:
            if os.path.isfile :
                try:
                    dic = {}
                    dic['conf'] = conf
                    dic['log'] = name
                    dic['time'] = 0
                    file_q.put(dic)
                except:
                    logging.error(traceback.format_exc())
    except:
        logging.error('获取{0}下的文件失败'.format(Observer_DIR))



def creat_md5(dic_data):
    global upload_file_success
    md5_data = ''
    try:
        with open(os.path.join(r'C:\Press\tmp\md5', 'md5'), 'w') as f:
            temp_list = []
            temp_str = ''
            i = 0
            for item, fileName in Sys_Config_File_Dict.items():
                temp_dict = {"name":fileName,'data':dic_data[fileName]}
                temp = getProfileMd5(temp_dict)
                temp_list.append(temp)
            temp_str = r''.join(temp_list)
            configSN = hashlib.md5(temp_str.encode('utf-8')).hexdigest()
            md5_data += configSN+'\n'
            profileSN = getProfileMd5(dic_data['profile'])
            md5_data += profileSN+'\n'
            for temp in temp_list:
                i+=1
                if(i<3):
                    md5_data+=temp+'\n'
                else:
                    md5_data+=temp
            f.write(md5_data)
    except:
        md5_data = None
        logging.error('creat_md5 fail,{0}'.format(traceback.format_exc()))
    return md5_data

g_file_update = None
def solver_file():
    global g_file_update,upload_file_success
    while True:
        try:
            dic = file_q.get()
            time_delay = dic['time']
            time.sleep(time_delay)
            if dic['conf']['success']:
                add_fold_and_upload(dic)
            else:
                print('获取配置文件失败')
                upload_file_success=False
                log_fail_deal(dic)
        except:
            pass
new_time = 0
last_time = 0
def read_conf(path):
    dic_conf={}
    success = True
    global g_state1
    g_state1 = 1
    for item, fileName in Sys_Config_File_Dict.items():
        temp_path = os.path.join(path,fileName)
        try:
            with open(temp_path,'rb') as f:
                dic_conf[fileName] = f.read()
        except:
            success =False
            print('file {0} read error'.format(temp_path))
    try:
        profile_name = getFileList(path)[1][0]
        temp_path = os.path.join(path,profile_name)
        with open(temp_path,'rb') as f:
            Pro_data = f.read()
    except:
        profile_name = None
        Pro_data =None
        success =False
        print("获取 profile file fail")
        logging.error("获取 profile file fail")
        logging.error(traceback.format_exc())

    dic_conf['profile'] = {"name":profile_name,'data':Pro_data}
    #print(dic_conf['profile']['name'])
    md5_name = r'C:\Press\tmp\md5\md5'
    dic_conf['md5'] = None
    new_flag_path = os.path.join(path,"_NewFlag")
    if(os.path.isfile(new_flag_path) and os.path.exists(new_flag_path)):
        #print("_NewFlag exist")
        os.remove(new_flag_path)
        if(os.path.isfile(md5_name) and os.path.exists(md5_name)):
            #print('del md5')
            os.remove(md5_name)
    else:
        #print("_NewFlag not exist")
        if(os.path.isfile(md5_name) and os.path.exists(md5_name)):
            with open(md5_name, 'r') as f:
                dic_conf['md5'] = f.read()
    if(dic_conf['md5'] == None):
        dic_conf['md5'] = creat_md5(dic_conf)
    dic_conf['success'] = success
    g_state1 = 0
    return dic_conf

class FileEventHandler(FileSystemEventHandler):
    def __init__(self):
        FileSystemEventHandler.__init__(self) 
    def on_moved(self, event): 
        pass
    def on_created(self, event): 
        global i,new_time,last_time
        if event.is_directory:
            pass
        else: 
            try:
                file_path,name=os.path.split(event.src_path)
                dic = {}
                dic['log'] = name
                dic['conf'] = read_conf(g_confdir)
                dic['time'] = 0.1
                file_q.put(dic)
            except:
                logging.error(traceback.format_exc())
             
    def on_deleted(self, event): 
        pass
    def on_modified(self, event):
        pass



def check_csv_environment():
    check_paths = [r'C:\Press\tmp\csv',r'C:\Press\tmp\csv\csv',r'C:\Press\tmp\csv\infrom',r'C:\Press\tmp\md5',r'C:\Press\Result\result']
    for check_path in check_paths:
        check_dir(check_path)

def check_errresult_dir(path):
    global upload_file_success
    # print('检测所需环境')
    if (os.path.exists(path)):
        pass
    else:
        # print("文件夹{0}不存在   创建文件夹".format(path))
        upload_file_success=False

def check_errresult_dir_flag():
    errresult_path = r'C:\Press\tmp\ErrResult'
    check_errresult_dir(errresult_path)


def run_upload_fail(board_id,run):
    global mainboard_id,g_file_update
    check_csv_environment()
    # g_file_update = threading.Semaphore()
    solver_file_t=threading.Thread(target=solver_file)
    mainboard_id=board_id
    observer = Observer() 
    event_handler = FileEventHandler() 
    observer.schedule(event_handler,Observer_DIR,True)
    add_all_file_upload()
    solver_file_t.start()
    observer.start()
    try:
        while run: 
            check_errresult_dir_flag()
            time.sleep(1) 
    except KeyboardInterrupt:
        #observer.stop()
        pass 
    observer.join()



def creat_csv_dir(path):
    files = os.listdir(path)
    for name in files:
        temp_path = os.path.join(path,name)
        openResultlogFile(temp_path)



def openResultlogFile_csv(fullName,csv_path):
    error_flag = 0
    # result_solve_success = False
    global g_srcfile,g_dstfile,g_csv_infrom,g_infrom_path,infrom_path
    g_csv_infrom = {}
    if os.path.exists(fullName) and os.path.isfile(fullName):
        MaxSize = os.path.getsize(fullName)
        #print(fullName, ' have opened. File size: ',MaxSize)
        for slove_i in range(3):
            try:
                error_flag = 1
                with open(fullName, 'rb') as f:
                    data = f.read()
                data_len = len(data)
                offset = 0
                error_flag = 2
                jsonString = ''
                file_header = []
                temp_data = []
                csv_data = []
                temp_data.append("Overview Information")
                file_header.append(temp_data)
                FileType,offset = readToZero(data, MaxSize, offset)
                # file_header["FileType"] = FileType
                temp_data = []
                temp_data.append("FileType:")
                temp_data.append(FileType)
                file_header.append(temp_data)
                #print('FileType',FileType)
                DeviceName, offset = readToZero(data, MaxSize, offset)
                # file_header["DeviceName"] = DeviceName
                temp_data = []
                temp_data.append("DeviceName:")
                temp_data.append(DeviceName)
                file_header.append(temp_data)
                #print('DeviceName',DeviceName)

                sw_version,offset=readToZero(data, MaxSize, offset)
                # file_header["sw_version"] = sw_version
                temp_data = []
                temp_data.append("sw_version:")
                temp_data.append(sw_version)
                file_header.append(temp_data)
                #print('sw_version',sw_version)
                hd_version,offset=readToZero(data, MaxSize, offset)
                # file_header["hd_version"] = hd_version
                temp_data = []
                temp_data.append("hd_version:")
                temp_data.append(hd_version)
                file_header.append(temp_data)
                #print('hd_version',hd_version)
                config_file_version,offset=readToZero(data, MaxSize, offset)
                # file_header["config_file_version"] = config_file_version
                temp_data = []
                temp_data.append("config_file_version:")
                temp_data.append(config_file_version)
                file_header.append(temp_data)

                #print('config_file_version',config_file_version)
                sn, offset = readToZero(data, MaxSize, offset)
                # file_header["sn"] = sn
                temp_data = []
                temp_data.append("PartID:")
                temp_data.append(sn)
                file_header.append(temp_data)


                #print('sn',sn)
                nc_id,offset=readToZero(data, MaxSize, offset)
                # file_header["nc_id"] = nc_id
                temp_data = []
                temp_data.append("NcID:")
                temp_data.append(nc_id)
                file_header.append(temp_data)

                #print('nc_id',nc_id)
                nc_ip,offset=readToZero(data, MaxSize, offset)
                # file_header["nc_ip"] = nc_ip
                temp_data = []
                temp_data.append("NcIP:")
                temp_data.append(nc_ip)
                file_header.append(temp_data)

                #print('nc_ip',nc_ip)
                nc_model,offset=readToZero(data, MaxSize, offset)
                # file_header["nc_model"] = nc_model
                temp_data = []
                temp_data.append("NcModel:")
                temp_data.append(nc_model)
                file_header.append(temp_data)
                error_flag = 3
                #print('nc_model',nc_model)
                timestamp, offset = readToZero(data, MaxSize, offset)
                # file_header["timestamp"] = timestamp
                temp_data = []
                temp_data.append("TimeStamp:")
                temp_data.append(timestamp)
                file_header.append(temp_data)
                temp_len = len(file_header) 
                if(temp_len< 19):
                    for i in  range(19 -temp_len):
                        temp_data = []
                        temp_data.append("")
                        file_header.append(temp_data)
                try:
                    timestamp=datetime.datetime.strptime(timestamp, '%Y-%m-%d-%H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')
                except:
                    #timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    logging.error('获取log文件的timestamp数据出错')
                #print('binaryDataLen',struct.unpack_from('<L', data, offset))
                offset += 4
                #print('sn','timestamp', sn, timestamp)
                #print('identity',struct.unpack_from('<B', data, offset))
                error_flag = 4
                for i in range(3):
                    slove_success = False 
                    try:
                        curve_result = read_curve_data(data, MaxSize, offset)
                        slove_success = True
                    except:
                        logging.error('解析曲线数据出错')
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        raise("error")
                    time.sleep(0.5)
                # print(result)
                error_flag = 5
                for i in range(3):
                    slove_success = False 
                    try:
                        ref_result = read_ref_result(data, MaxSize, offset)
                        slove_success = True
                    except:
                        logging.error('解析 ref_result 出错')
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        raise("error")
                    time.sleep(0.5)
                
                # print(result)
                error_flag = 6
                for i in range(3):
                    slove_success = False 
                    try:
                        ref_conf = read_ref_conf(data, MaxSize, offset)
                        slove_success = True
                    except:
                        logging.error('解析ref_conf出错')
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        raise("error")
                    time.sleep(0.5)
                # print(result)
                error_flag = 7
                for i in range(3):
                    slove_success = False 
                    try:
                        eo_conf = read_EoConf(data, MaxSize, offset)
                        slove_success = True
                    except:
                        logging.error('解析eo_conf出错')
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        raise("error")
                    time.sleep(0.5)
                error_flag = 8
                for i in range(3):
                    slove_success = False 
                    try:
                        eo_result = read_eo_result(data, MaxSize, offset)
                        slove_success = True
                    except:
                        logging.error('解析eo_result出错')
                        pass
                    if slove_success:
                        break
                    elif (i ==2):
                        raise("error")
                    time.sleep(0.5)           
                # print(result)
                try:
                    error_flag = 9
                    result,python_result =read_python_data(data, MaxSize, offset)
                    # print(result)
                    csv_data.append(file_header)
                    csv_data.append(python_result)
                    csv_data.append(ref_conf)
                    csv_data.append(eo_conf)
                    csv_data.append(ref_result)
                    csv_data.append(eo_result)
                    csv_data.append(curve_result)
                    # csv_name = os.path.join(csv_path,)
                    # write_csv("test.csv",csv_data)
                    error_flag = 10
                    path_name , file_name = os.path.split(fullName)
                    (filename, extension) = os.path.splitext(file_name)
                    csv_name = os.path.join(csv_path,filename+'.csv')
                    g_infrom_path = os.path.join(infrom_path,filename+'.json')
                    g_csv_infrom['is_pass']=result['is_pass']
                    g_csv_infrom['DeviceName']=DeviceName
                    g_csv_infrom['TimeStamp']=timestamp
                    g_csv_infrom['nc_id']=nc_id
                    g_csv_infrom['csv_name'] = filename + '.csv'
                    creat_csv_file(csv_name,csv_data)
                    error_flag = 11
                except:
                    #result={}
                    logging.error('read_python_data():fail {0},errorflag = {1}'.format(traceback.format_exc(),error_flag))
                offset += 28
                sampleCount, = struct.unpack_from('<L', data, offset)
                offset += 4
                #offset += 4
                '''for i in range(0, sampleCount):
                    t,x,y = struct.unpack_from('<3f', data, offset)
                    offset += 12
                    str = '[{0},{1},{2}],'.format(t, x, y)'''
                #print('total lenght: ', len(jsonString))
                return {'sn'                        :sn,
                        'TimeStamp'                 :timestamp,
                        'Operator'                  :result['Operator'],
                        'RawWaveformByteOffset'     :offset,
                        'SampleCount'               :sampleCount,
                        'ResultList'                :'',

                        'is_pass'                   :result['is_pass'],
                        'Operator'                  :result['Operator'],
                        'time_min_t'                :result['time_min_t'],
                        'time_min_x'                :result['time_min_x'],
                        'time_min_force'            :result['time_min_force'],
                        'time_max_t'                :result['time_max_t'],
                        'time_max_x'                :result['time_max_x'],
                        'time_max_force'            :result['time_max_force'],
                        'time_average'              :result['time_average'],
                        'x_min_t'                   :result['x_min_t'],
                        'x_min_x'                   :result['x_min_x'],
                        'x_min_force'               :result['x_min_force'],
                        'x_max_t'                   :result['x_max_t'],
                        'x_max_x'                   :result['x_max_x'],
                        'x_max_force'               :result['x_max_force'],
                        'x_average'                 :result['x_average'],
                        'force_min_t'               :result['force_min_t'],
                        'force_min_x'               :result['force_min_x'],
                        'force_min_force'           :result['force_min_force'],
                        'force_max_t'               :result['force_max_t'],
                        'force_max_x'               :result['force_max_x'],
                        'force_max_force'           :result['force_max_force'],
                        'force_average'             :result['force_average'],

                        'DeviceName'                :DeviceName,
                        'config_file_version'       :config_file_version,
                        'sw_version'                :sw_version,
                        'hd_version'                :hd_version,
                        'nc_id'                     :nc_id,
                        'nc_ip'                     :nc_ip,
                        'nc_model'                  :nc_model
                        }
            except:
                if (slove_i == 2):
                    logging.error('openResultlogFile fail,解析{0}文件出错，errorflag = {1},datalen = {2}'.format(fullName,error_flag,data_len))
                    g_srcfile=fullName
                    fpath,fname=os.path.split(g_srcfile)
                    shutil.move(g_srcfile,os.path.join(g_dstfile,fname))
                else:
                    time.sleep(1)
    else:
        logging.error('openResultlogFile fail,{0}不存或不是文件'.format(fullName))

if __name__ == "__main__":
    # creat_csv_dir(r"I:\git\result")
    # run_upload_fail('10',True)
    openResultlogFile(r"C:\Users\<USER>\Desktop\c\2025-04-08-17-53-42\OP5220-001-151001230-CBQNA-25022110655                       .log")
    # # add_all_file_upload()
    # # check_csv_environment()
    # openResultlogFile(r"I:\git\result\12OP101-001-04-01-09-25-25-0001-113090070ee73b6e56dc860b785938f4.log")
    # # add_all_file_upload()
    # # run_upload_fail(1,1)
    # start = time.time()
    # print()
    
    # # check_config_profile_md5()
    # # read_conf(r'C:\Press\tmp\Config')
    
    # time.sleep(1)
    # end = time.time()
    # print(end -start)
    # pass
    base_path = os.getcwd()
    log_path = os.path.join(base_path,'log')
    csv_path = os.path.join(base_path,'csv')
    if(os.path.exists(csv_path) == False):
        os.makedirs(csv_path)
    log_files = os.listdir(log_path)
    for log_file in log_files:
        path = os.path.join(log_path,log_file)
        openResultlogFile_csv(path,csv_path)
