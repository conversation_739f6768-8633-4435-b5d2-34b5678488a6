#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EOCalc 新协议解析示例
根据最新的协议文档重新实现 eo_type == 7 的处理逻辑
"""

import struct

def parse_eo_calc_type_7(rawBytes, offset, temp_data):
    """
    解析 eo_type == 7 (EOCalc) 的完整数据结构
    
    根据新协议包含两个主要部分:
    1. EOContent - 基本EO信息
    2. EOCalc ResultDataExt - EOCalc特有的扩展数据
    
    Args:
        rawBytes: 原始字节数据
        offset: 当前读取位置
        temp_data: 输出数据列表
        
    Returns:
        int: 更新后的offset位置
    """
    
    print("开始解析 EOCalc (eo_type=7) 数据...")
    temp_data.append("=== EOCalc 评估框 ===")
    
    # ==========================================
    # 第一部分: EOContent 
    # ==========================================
    temp_data.append("--- EOContent ---")
    
    # EoIndex (U8)
    eo_index = struct.unpack_from('<B', rawBytes, offset)[0]
    temp_data.append("EoIndex:")
    temp_data.append(eo_index)
    offset += 1
    print(f"  EoIndex: {eo_index}")
    
    # EoType (U8) - 应该是7
    eo_type_check = struct.unpack_from('<B', rawBytes, offset)[0]
    temp_data.append("EoType:")
    temp_data.append(eo_type_check)
    offset += 1
    print(f"  EoType: {eo_type_check}")
    
    if eo_type_check != 7:
        print(f"  ⚠️  警告: EoType应该是7，但读取到{eo_type_check}")
    
    # EoResult (Bool)
    eo_result = struct.unpack_from('<B', rawBytes, offset)[0]
    temp_data.append("EoResult:")
    temp_data.append("PASS" if eo_result else "FAIL")
    offset += 1
    print(f"  EoResult: {'PASS' if eo_result else 'FAIL'}")
    
    # EoErrorCode (U64)
    eo_error_code = struct.unpack_from('<Q', rawBytes, offset)[0]
    temp_data.append("EoErrorCode:")
    temp_data.append(hex(eo_error_code))
    offset += 8
    print(f"  EoErrorCode: {hex(eo_error_code)}")
    
    # ==========================================
    # 第二部分: EOCalc ResultDataExt
    # ==========================================
    temp_data.append("--- EOCalc ResultDataExt ---")
    
    # EoCalcCount (U8) - N(1,2) 表示有1到2个结果项
    eo_calc_count = struct.unpack_from('<B', rawBytes, offset)[0]
    temp_data.append("EoCalcCount:")
    temp_data.append(eo_calc_count)
    offset += 1
    print(f"  EoCalcCount: {eo_calc_count}")
    
    # 验证计数范围
    if eo_calc_count < 1 or eo_calc_count > 2:
        print(f"  ⚠️  警告: EoCalcCount应该在1-2之间，但读取到{eo_calc_count}")
    
    # 读取 EOCalcResult 数组 (1到N个)
    if eo_calc_count > 0:
        for i in range(eo_calc_count):
            print(f"  解析第 {i+1} 个 EOCalcResult...")
            temp_data.append(f"--- EOCalcResult{i+1} ---")
            
            # ==========================================
            # EOResultItem 结构
            # ==========================================
            
            # VarIsOK (U8)
            var_is_ok = struct.unpack_from('<B', rawBytes, offset)[0]
            temp_data.append("VarIsOK:")
            temp_data.append("OK" if var_is_ok else "NG")
            offset += 1
            print(f"    VarIsOK: {'OK' if var_is_ok else 'NG'}")
            
            # RealVar (F32)
            real_var = struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data.append("RealVar:")
            temp_data.append(real_var)
            offset += 4
            print(f"    RealVar: {real_var}")
            
            # SetMinVar (F32)
            set_min_var = struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data.append("SetMinVar:")
            temp_data.append(set_min_var)
            offset += 4
            print(f"    SetMinVar: {set_min_var}")
            
            # SetMaxVar (F32)
            set_max_var = struct.unpack_from('<f', rawBytes, offset)[0]
            temp_data.append("SetMaxVar:")
            temp_data.append(set_max_var)
            offset += 4
            print(f"    SetMaxVar: {set_max_var}")
            
            # 验证数值范围
            if var_is_ok and not (set_min_var <= real_var <= set_max_var):
                print(f"    ⚠️  警告: VarIsOK为OK但RealVar({real_var})不在范围[{set_min_var}, {set_max_var}]内")
    
    print("EOCalc 数据解析完成")
    return offset

def create_test_eo_calc_data():
    """
    创建测试用的 EOCalc 二进制数据
    """
    data = bytearray()
    
    # EOContent 部分
    data.extend(struct.pack('<B', 1))        # EoIndex = 1
    data.extend(struct.pack('<B', 7))        # EoType = 7 (EOCalc)
    data.extend(struct.pack('<B', 1))        # EoResult = 1 (PASS)
    data.extend(struct.pack('<Q', 0x0000))   # EoErrorCode = 0 (无错误)
    
    # EOCalc ResultDataExt 部分
    data.extend(struct.pack('<B', 2))        # EoCalcCount = 2 (两个结果项)
    
    # 第一个 EOResultItem
    data.extend(struct.pack('<B', 1))        # VarIsOK = 1 (OK)
    data.extend(struct.pack('<f', 125.5))    # RealVar = 125.5
    data.extend(struct.pack('<f', 100.0))    # SetMinVar = 100.0
    data.extend(struct.pack('<f', 150.0))    # SetMaxVar = 150.0
    
    # 第二个 EOResultItem
    data.extend(struct.pack('<B', 0))        # VarIsOK = 0 (NG)
    data.extend(struct.pack('<f', 85.2))     # RealVar = 85.2
    data.extend(struct.pack('<f', 90.0))     # SetMinVar = 90.0
    data.extend(struct.pack('<f', 110.0))    # SetMaxVar = 110.0
    
    return bytes(data)

def test_eo_calc_parsing():
    """
    测试 EOCalc 解析功能
    """
    print("=" * 50)
    print("EOCalc 新协议解析测试")
    print("=" * 50)
    
    # 创建测试数据
    test_data = create_test_eo_calc_data()
    print(f"测试数据长度: {len(test_data)} 字节")
    print(f"测试数据 (hex): {test_data.hex()}")
    print()
    
    # 解析数据
    offset = 0
    temp_data = []
    
    try:
        final_offset = parse_eo_calc_type_7(test_data, offset, temp_data)
        
        print("\n" + "=" * 50)
        print("解析结果输出")
        print("=" * 50)
        
        for i in range(0, len(temp_data), 2):
            if i + 1 < len(temp_data):
                print(f"{temp_data[i]} {temp_data[i+1]}")
            else:
                print(f"{temp_data[i]}")
        
        print(f"\n解析统计:")
        print(f"  起始offset: {offset}")
        print(f"  结束offset: {final_offset}")
        print(f"  数据总长度: {len(test_data)}")
        print(f"  解析字节数: {final_offset - offset}")
        
        if final_offset == len(test_data):
            print("  ✅ 解析成功，所有数据都被正确读取")
        else:
            print(f"  ❌ 解析可能有问题，剩余 {len(test_data) - final_offset} 字节未读取")
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()

def show_protocol_structure():
    """
    显示协议结构说明
    """
    print("=" * 60)
    print("EOCalc (eo_type=7) 协议结构")
    print("=" * 60)
    print("""
1. EOContent:
   - EoIndex    (U8)  : EO索引
   - EoType     (U8)  : EO类型 (应该是7)
   - EoResult   (Bool): EO结果 (0=FAIL, 1=PASS)
   - EoErrorCode(U64) : 错误代码

2. EOCalc ResultDataExt:
   - EoCalcCount(U8)  : 计算结果数量 N(1,2)
   
   对于每个 EOCalcResult (重复N次):
   - VarIsOK    (U8)  : 变量是否OK (0=NG, 1=OK)
   - RealVar    (F32) : 实际值
   - SetMinVar  (F32) : 设置最小值
   - SetMaxVar  (F32) : 设置最大值

总字节数: 13 + N*13 字节 (N=1或2)
""")

if __name__ == "__main__":
    show_protocol_structure()
    test_eo_calc_parsing()
