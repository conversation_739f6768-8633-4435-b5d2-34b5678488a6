#!/bin/bash

# SQLite WAL模式数据库重命名脚本
# 自动重命名主文件及其关联的WAL和SHM文件

# 使用方法: ./rename_wal_db.sh old_name.db new_name.db

if [ $# -ne 2 ]; then
    echo "使用方法: $0 <旧文件名> <新文件名>"
    echo "示例: $0 press.lx press_backup.lx"
    exit 1
fi

OLD_NAME="$1"
NEW_NAME="$2"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查源文件是否存在
if [ ! -f "$OLD_NAME" ]; then
    print_error "源文件不存在: $OLD_NAME"
    exit 1
fi

print_info "开始重命名 SQLite WAL 模式数据库文件..."
print_info "源文件: $OLD_NAME"
print_info "目标文件: $NEW_NAME"

# 检查目标文件是否已存在
if [ -f "$NEW_NAME" ]; then
    read -p "目标文件 $NEW_NAME 已存在，是否覆盖? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        print_info "操作已取消"
        exit 0
    fi
fi

# 重命名主文件
print_info "重命名主数据库文件..."
if mv "$OLD_NAME" "$NEW_NAME"; then
    print_success "主文件重命名成功: $OLD_NAME -> $NEW_NAME"
else
    print_error "主文件重命名失败"
    exit 1
fi

# 重命名WAL文件（如果存在）
if [ -f "${OLD_NAME}-wal" ]; then
    print_info "重命名WAL文件..."
    if mv "${OLD_NAME}-wal" "${NEW_NAME}-wal"; then
        print_success "WAL文件重命名成功: ${OLD_NAME}-wal -> ${NEW_NAME}-wal"
    else
        print_error "WAL文件重命名失败"
        # 回滚主文件
        mv "$NEW_NAME" "$OLD_NAME"
        exit 1
    fi
else
    print_info "WAL文件不存在，跳过"
fi

# 重命名SHM文件（如果存在）
if [ -f "${OLD_NAME}-shm" ]; then
    print_info "重命名SHM文件..."
    if mv "${OLD_NAME}-shm" "${NEW_NAME}-shm"; then
        print_success "SHM文件重命名成功: ${OLD_NAME}-shm -> ${NEW_NAME}-shm"
    else
        print_error "SHM文件重命名失败"
        # 回滚
        mv "$NEW_NAME" "$OLD_NAME"
        if [ -f "${NEW_NAME}-wal" ]; then
            mv "${NEW_NAME}-wal" "${OLD_NAME}-wal"
        fi
        exit 1
    fi
else
    print_info "SHM文件不存在，跳过"
fi

print_success "所有文件重命名完成！"

# 显示结果
print_info "重命名结果:"
ls -la "$NEW_NAME"* 2>/dev/null | while read line; do
    echo "  $line"
done

print_info "建议在重命名后检查数据库完整性:"
echo "  sqlite3 \"$NEW_NAME\" \"PRAGMA integrity_check;\""
echo "  sqlite3 \"$NEW_NAME\" \"PRAGMA wal_checkpoint(FULL);\""
