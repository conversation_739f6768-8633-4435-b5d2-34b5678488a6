// C++ WAL文件管理器
// 用于处理SQLite WAL模式数据库的文件重命名和管理

#include <iostream>
#include <string>
#include <filesystem>
#include <vector>
#include <sqlite3.h>

namespace fs = std::filesystem;

class WALFileManager {
public:
    // 重命名WAL模式数据库的所有相关文件
    static bool renameWALDatabase(const std::string& oldPath, const std::string& newPath) {
        std::vector<std::string> filesToRename;
        std::vector<std::pair<std::string, std::string>> renamedFiles;
        
        // 检查主文件
        if (!fs::exists(oldPath)) {
            std::cerr << "错误: 源文件不存在: " << oldPath << std::endl;
            return false;
        }
        
        filesToRename.push_back(oldPath);
        
        // 检查WAL文件
        std::string oldWal = oldPath + "-wal";
        std::string newWal = newPath + "-wal";
        if (fs::exists(oldWal)) {
            filesToRename.push_back(oldWal);
        }
        
        // 检查SHM文件
        std::string oldShm = oldPath + "-shm";
        std::string newShm = newPath + "-shm";
        if (fs::exists(oldShm)) {
            filesToRename.push_back(oldShm);
        }
        
        std::cout << "准备重命名 " << filesToRename.size() << " 个文件..." << std::endl;
        
        try {
            // 执行重命名
            for (const auto& file : filesToRename) {
                std::string newFile;
                if (file == oldPath) {
                    newFile = newPath;
                } else if (file == oldWal) {
                    newFile = newWal;
                } else if (file == oldShm) {
                    newFile = newShm;
                }
                
                std::cout << "重命名: " << file << " -> " << newFile << std::endl;
                fs::rename(file, newFile);
                renamedFiles.push_back({file, newFile});
            }
            
            std::cout << "所有文件重命名成功！" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "重命名失败: " << e.what() << std::endl;
            
            // 回滚已重命名的文件
            std::cout << "正在回滚..." << std::endl;
            for (auto it = renamedFiles.rbegin(); it != renamedFiles.rend(); ++it) {
                try {
                    fs::rename(it->second, it->first);
                    std::cout << "回滚: " << it->second << " -> " << it->first << std::endl;
                } catch (const std::exception& rollbackError) {
                    std::cerr << "回滚失败: " << rollbackError.what() << std::endl;
                }
            }
            return false;
        }
    }
    
    // 检查数据库是否处于WAL模式
    static bool isWALMode(const std::string& dbPath) {
        sqlite3* db = nullptr;
        int rc = sqlite3_open_v2(dbPath.c_str(), &db, SQLITE_OPEN_READONLY, nullptr);
        
        if (rc != SQLITE_OK) {
            std::cerr << "无法打开数据库: " << sqlite3_errmsg(db) << std::endl;
            if (db) sqlite3_close(db);
            return false;
        }
        
        sqlite3_stmt* stmt = nullptr;
        rc = sqlite3_prepare_v2(db, "PRAGMA journal_mode;", -1, &stmt, nullptr);
        
        bool isWAL = false;
        if (rc == SQLITE_OK && sqlite3_step(stmt) == SQLITE_ROW) {
            const char* mode = (const char*)sqlite3_column_text(stmt, 0);
            isWAL = (mode && std::string(mode) == "wal");
        }
        
        if (stmt) sqlite3_finalize(stmt);
        sqlite3_close(db);
        
        return isWAL;
    }
    
    // 强制WAL检查点，将WAL内容合并到主文件
    static bool checkpointWAL(const std::string& dbPath) {
        sqlite3* db = nullptr;
        int rc = sqlite3_open(dbPath.c_str(), &db);
        
        if (rc != SQLITE_OK) {
            std::cerr << "无法打开数据库: " << sqlite3_errmsg(db) << std::endl;
            if (db) sqlite3_close(db);
            return false;
        }
        
        std::cout << "执行WAL检查点..." << std::endl;
        
        // 执行完整检查点
        rc = sqlite3_exec(db, "PRAGMA wal_checkpoint(FULL);", nullptr, nullptr, nullptr);
        
        if (rc == SQLITE_OK) {
            std::cout << "WAL检查点完成" << std::endl;
        } else {
            std::cerr << "WAL检查点失败: " << sqlite3_errmsg(db) << std::endl;
        }
        
        sqlite3_close(db);
        return rc == SQLITE_OK;
    }
    
    // 列出数据库相关的所有文件
    static void listDatabaseFiles(const std::string& dbPath) {
        std::cout << "数据库相关文件:" << std::endl;
        
        // 主文件
        if (fs::exists(dbPath)) {
            auto size = fs::file_size(dbPath);
            std::cout << "  主文件: " << dbPath << " (" << size << " bytes)" << std::endl;
        }
        
        // WAL文件
        std::string walPath = dbPath + "-wal";
        if (fs::exists(walPath)) {
            auto size = fs::file_size(walPath);
            std::cout << "  WAL文件: " << walPath << " (" << size << " bytes)" << std::endl;
        }
        
        // SHM文件
        std::string shmPath = dbPath + "-shm";
        if (fs::exists(shmPath)) {
            auto size = fs::file_size(shmPath);
            std::cout << "  SHM文件: " << shmPath << " (" << size << " bytes)" << std::endl;
        }
    }
};

// 使用示例
int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cout << "使用方法: " << argv[0] << " <旧文件名> <新文件名>" << std::endl;
        std::cout << "示例: " << argv[0] << " press.lx press_backup.lx" << std::endl;
        return 1;
    }
    
    std::string oldPath = argv[1];
    std::string newPath = argv[2];
    
    std::cout << "=== SQLite WAL数据库文件管理器 ===" << std::endl;
    
    // 检查是否为WAL模式
    if (WALFileManager::isWALMode(oldPath)) {
        std::cout << "检测到WAL模式数据库" << std::endl;
        
        // 显示当前文件
        std::cout << "\n重命名前:" << std::endl;
        WALFileManager::listDatabaseFiles(oldPath);
        
        // 可选：执行检查点
        char choice;
        std::cout << "\n是否在重命名前执行WAL检查点? (y/N): ";
        std::cin >> choice;
        if (choice == 'y' || choice == 'Y') {
            WALFileManager::checkpointWAL(oldPath);
        }
        
        // 执行重命名
        std::cout << "\n开始重命名..." << std::endl;
        if (WALFileManager::renameWALDatabase(oldPath, newPath)) {
            std::cout << "\n重命名后:" << std::endl;
            WALFileManager::listDatabaseFiles(newPath);
        }
    } else {
        std::cout << "这不是WAL模式数据库，执行简单重命名..." << std::endl;
        try {
            fs::rename(oldPath, newPath);
            std::cout << "重命名成功: " << oldPath << " -> " << newPath << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "重命名失败: " << e.what() << std::endl;
            return 1;
        }
    }
    
    return 0;
}
