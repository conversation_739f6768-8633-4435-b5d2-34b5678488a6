// 完整的数据库迁移代码 - 方案1（改进的清理和重试机制）
// 包含所有必要的头文件和完整实现

#include <sqlite3.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <stdexcept>
#include <httplib.h>
#include "dbi.h"  // 假设这里包含了md5函数声明

// 配置常量
const std::string OLD_DB_PATH = "/root/press.lx";
const std::string NEW_DB_PATH = "/root/press_new.lx";
const std::vector<std::string> TABLES_TO_PROCESS = {
    "HardwareConfiguration",
};

class update_exe {
private:
    DBI* dbi;

public:
    update_exe(DBI* db);
    ~update_exe();
    void tableCheck();
    void bindServe(httplib::Server& svr);
    void handle(const httplib::Request& req, httplib::Response& res);
    
private:
    // 辅助函数
    bool execute_sql(sqlite3* db, const std::string& sql);
    bool detach_database_with_retry(sqlite3* db, const std::string& alias, int max_attempts = 5);
    void update_exe_tmp(DBI* db); // 假设这个函数存在
};

// 一个辅助函数，用于执行简单的SQL命令并检查错误
bool update_exe::execute_sql(sqlite3* db, const std::string& sql) {
    char* zErrMsg = nullptr;
    int rc = sqlite3_exec(db, sql.c_str(), nullptr, 0, &zErrMsg);
    if (rc != SQLITE_OK) {
        std::cout << "SQL 错误: " << zErrMsg << std::endl;
        std::cout << "失败的SQL语句: " << sql << std::endl;
        sqlite3_free(zErrMsg);
        return false;
    }
    return true;
}

// 带重试机制的分离数据库函数
bool update_exe::detach_database_with_retry(sqlite3* db, const std::string& alias, int max_attempts) {
    for (int attempt = 0; attempt < max_attempts; ++attempt) {
        // 在每次尝试前进行清理
        if (attempt > 0) {
            std::cout << "  尝试 " << (attempt + 1) << ": 执行清理操作..." << std::endl;
            
            // 强制释放内存和清理缓存
            sqlite3_db_release_memory(db);
            sqlite3_exec(db, "PRAGMA shrink_memory;", nullptr, 0, nullptr);
            
            // 等待更长时间
            std::this_thread::sleep_for(std::chrono::milliseconds(1500));
        }
        
        std::string detach_sql = "DETACH DATABASE " + alias + ";";
        char* zErrMsg = nullptr;
        int rc = sqlite3_exec(db, detach_sql.c_str(), nullptr, 0, &zErrMsg);
        
        if (rc == SQLITE_OK) {
            std::cout << "  成功分离数据库" << std::endl;
            return true;
        }
        
        std::string error_msg = zErrMsg ? zErrMsg : "未知错误";
        sqlite3_free(zErrMsg);
        
        if (error_msg.find("locked") != std::string::npos && attempt < max_attempts - 1) {
            std::cout << "  尝试 " << (attempt + 1) << ": 数据库被锁定，准备重试..." << std::endl;
        } else {
            std::cout << "分离数据库失败: " << error_msg << std::endl;
            return false;
        }
    }
    return false;
}

update_exe::update_exe(DBI* db) : dbi(db)
{
    update_exe_tmp(db);
    tableCheck();
}

update_exe::~update_exe()
{
    // 析构函数实现
}

void update_exe::tableCheck()
{
    // 表检查实现
    // 这里可以添加创建升级记录表的代码
    /*
    std::string sql1{R"(CREATE TABLE IF NOT EXISTS UpgradeInfoRecords (
        id           INTEGER PRIMARY KEY AUTOINCREMENT, 
        version      VARCHAR(256) NOT NULL,                
        info         VARCHAR(512) NOT NULL,                  
        upgrade_time DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')),
        user         VARCHAR(256),                            
        remarks      VARCHAR(256)                          
    );)"};
    */
}

void update_exe::bindServe(httplib::Server& svr)
{
    // 添加OPTIONS方法处理CORS预检请求
    svr.Options("/sys/misc/update", [](const auto& req, auto& res) {
        res.set_header("Access-Control-Allow-Origin", "*");
        res.set_header("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
        res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
        res.set_header("Access-Control-Max-Age", "86400");
        res.status = 200;
    });

    // 处理POST请求
    svr.Post("/sys/misc/update", [this](const auto& req, auto& res) {
        res.set_header("Access-Control-Allow-Origin", "*");
        res.set_header("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
        res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
        res.set_header("Access-Control-Max-Age", "86400");
        
        handle(req, res);
    });
}

void update_exe::handle(const httplib::Request& req, httplib::Response& res)
{
    sqlite3* db = nullptr;
    int rc;
    bool db_attached = false;

    // 1. 连接到新数据库
    rc = sqlite3_open(NEW_DB_PATH.c_str(), &db);
    if (rc != SQLITE_OK) {
        std::cerr << "无法打开数据库: " << sqlite3_errmsg(db) << std::endl;
        std::cerr << "请确认文件路径正确，并且您有足够的权限（可能需要使用sudo运行）。" << std::endl;
        if (db) sqlite3_close(db);
        return;
    }
    std::cout << "已成功连接到新数据库: " << NEW_DB_PATH << std::endl;

    // 设置忙等待超时（3秒）
    sqlite3_busy_timeout(db, 3000);

    // 注册 MD5 函数
    rc = sqlite3_create_function(
        db,
        "md5",
        1,
        SQLITE_UTF8,
        nullptr,
        md5,  // 使用 dbi.h 中声明的 md5 函数
        nullptr,
        nullptr
    );

    if (rc != SQLITE_OK) {
        std::cerr << "无法创建 md5 函数: " << sqlite3_errmsg(db) << std::endl;
        sqlite3_close(db);
        return;
    }
    std::cout << "已成功注册自定义函数 'md5'。" << std::endl;

    // 2. 启动事务，保证操作的原子性
    if (!execute_sql(db, "BEGIN IMMEDIATE;")) {
        sqlite3_close(db);
        return;
    }
    std::cout << "事务已开始。" << std::endl << std::endl;

    bool success = true;

    // 3. 【步骤 1】清空新数据库中的目标表
    std::cout << "--- 步骤 1: 正在清空新数据库中的目标表 ---" << std::endl;
    for (const auto& table_name : TABLES_TO_PROCESS) {
        std::cout << "  -> 正在删除表 '" << table_name << "' 中的所有数据..." << std::endl;
        std::string delete_sql = "DELETE FROM \"" + table_name + "\";";
        if (!execute_sql(db, delete_sql)) {
            success = false;
            break;
        }
    }

    // 4. 【步骤 2】附加旧数据库
    if (success) {
        std::cout << std::endl << "--- 步骤 2: 正在从旧数据库复制数据 ---" << std::endl;
        std::cout << "正在附加旧数据库: " << OLD_DB_PATH << std::endl;
        std::string attach_sql = "ATTACH DATABASE '" + OLD_DB_PATH + "' AS old_db;";
        if (execute_sql(db, attach_sql)) {
            db_attached = true;
        } else {
            success = false;
        }
    }

    // 5. 【步骤 3】从旧数据库复制数据到新数据库
    if (success) {
        for (const auto& table_name : TABLES_TO_PROCESS) {
            std::cout << "  -> 正在复制数据到表 '" << table_name << "'..." << std::endl;
            std::string insert_sql = "INSERT INTO main.\"" + table_name + "\" SELECT * FROM old_db.\"" + table_name + "\";";
            if (!execute_sql(db, insert_sql)) {
                success = false;
                break;
            }
        }
    }

    // 6. 【步骤 4】分离旧数据库前的清理工作
    if (db_attached) {
        std::cout << "正在准备分离旧数据库..." << std::endl;
        
        // 强制清理SQLite内部缓存和状态
        execute_sql(db, "PRAGMA shrink_memory;");
        execute_sql(db, "PRAGMA cache_size = 0;");
        execute_sql(db, "PRAGMA cache_size = -2000;"); // 恢复默认缓存
        
        // 确保所有语句都已完成
        sqlite3_db_release_memory(db);
        
        std::cout << "正在分离旧数据库..." << std::endl;
        if (!detach_database_with_retry(db, "old_db")) {
            std::cerr << "警告: 无法分离旧数据库，但数据复制可能已完成" << std::endl;
            // 不将此设为失败，因为数据可能已经复制成功
        }
    }

    // 7. 根据成功与否，提交或回滚事务
    if (success) {
        std::cout << "\n正在提交事务..." << std::endl;
        if (execute_sql(db, "COMMIT;")) {
            std::cout << "\n操作成功完成！数据已清空并从旧数据库重新载入。" << std::endl;
        } else {
            std::cerr << "致命错误：提交事务失败！数据库可能处于不一致状态。" << std::endl;
            execute_sql(db, "ROLLBACK;"); // 尝试回滚
        }
    } else {
        std::cerr << "\n操作中发生错误，正在回滚所有更改..." << std::endl;
        execute_sql(db, "ROLLBACK;");
        std::cerr << "操作已回滚。新数据库未被修改。" << std::endl;
    }

    // 8. 关闭数据库连接
    sqlite3_close(db);
    
    // 设置HTTP响应
    if (success) {
        res.set_content("{\"status\":\"success\",\"message\":\"数据迁移完成\"}", "application/json");
    } else {
        res.status = 500;
        res.set_content("{\"status\":\"error\",\"message\":\"数据迁移失败\"}", "application/json");
    }
}

// 假设的 update_exe_tmp 函数实现
void update_exe::update_exe_tmp(DBI* db) {
    // 这里添加初始化代码
    // 根据你的实际需求实现
}
