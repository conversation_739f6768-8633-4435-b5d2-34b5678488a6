# EOCalc 类型处理示例代码
# 这是我为 read_eo_result 函数添加的 eo_type == 7 处理逻辑

import struct

def read_Resultltem(rawBytes, offset):
    """
    读取 EOResultItem 结构
    返回: (新的offset, ResultItem字典)
    """
    ResultItem = {}
    varispass = struct.unpack_from('<B', rawBytes, offset)[0]
    offset += 1
    RealVar = struct.unpack_from('<f', rawBytes, offset)[0]
    offset += 4
    MinSet = struct.unpack_from('<f', rawBytes, offset)[0]
    offset += 4
    MaxSet = struct.unpack_from('<f', rawBytes, offset)[0]
    offset += 4
    
    ResultItem["Ispass"] = varispass
    ResultItem["Real Value"] = RealVar
    ResultItem["Min Range"] = MinSet
    ResultItem["Max Range"] = MaxSet
    
    return offset, ResultItem

def process_eo_calc_type(rawBytes, offset, temp_data):
    """
    处理 eo_type == 7 (EOCalc) 的数据解析
    
    根据协议:
    - EOCalcVarCount: U8 (1字节) - 表示有多少个EOResultItem
    - EOCalcResult 1: EOResultItem 结构
    - ...
    - EOCalcResult N: EOResultItem 结构
    
    其中 EOResultItem 包含:
    - VarIsOK: U8 (1字节)
    - RealVar: F32 (4字节)
    - SetMinVar: F32 (4字节)  
    - SetMaxVar: F32 (4字节)
    """
    
    # 读取 EOCalcVarCount (U8)
    eo_calc_var_count = struct.unpack_from('<B', rawBytes, offset)[0]
    temp_data.append("EOCalcVarCount:")
    temp_data.append(eo_calc_var_count)
    offset += 1
    
    print(f"EOCalc: 发现 {eo_calc_var_count} 个计算结果项")
    
    # 读取 EOCalcResult 数组
    if eo_calc_var_count > 0:
        for i in range(eo_calc_var_count):
            print(f"  正在读取第 {i+1} 个 EOCalcResult...")
            
            # 读取每个 EOResultItem
            offset, result_item = read_Resultltem(rawBytes, offset)
            
            # 添加结果项数据到输出
            temp_data.append(f"EOCalcResult{i+1}_VarIsOK:")
            temp_data.append(result_item["Ispass"])
            
            temp_data.append(f"EOCalcResult{i+1}_RealVar:")
            temp_data.append(result_item["Real Value"])
            
            temp_data.append(f"EOCalcResult{i+1}_SetMinVar:")
            temp_data.append(result_item["Min Range"])
            
            temp_data.append(f"EOCalcResult{i+1}_SetMaxVar:")
            temp_data.append(result_item["Max Range"])
            
            print(f"    VarIsOK: {result_item['Ispass']}")
            print(f"    RealVar: {result_item['Real Value']}")
            print(f"    SetMinVar: {result_item['Min Range']}")
            print(f"    SetMaxVar: {result_item['Max Range']}")
    
    return offset

# 在 read_eo_result 函数中的使用示例:
def example_usage_in_read_eo_result():
    """
    这是在 read_eo_result 函数中如何使用的示例
    """
    # ... 其他代码 ...
    
    # 在现有的 elif 条件链中添加:
    """
    elif(eo_type == 7):  # EOCalc
        # 读取 EOCalcVarCount (U8)
        eo_calc_var_count = struct.unpack_from('<B', rawBytes, offset)[0]
        temp_data.append("EOCalcVarCount:")
        temp_data.append(eo_calc_var_count)
        offset += 1
        
        # 读取 EOCalcResult 数组
        if eo_calc_var_count > 0:
            for i in range(eo_calc_var_count):
                # 读取每个 EOResultItem
                offset, result_item = read_Resultltem(rawBytes, offset)
                
                # 添加结果项数据
                for result_key in result_item:
                    temp_data.append("EOCalcResult{0}_{1}:".format(i+1, result_key))
                    temp_data.append(result_item[result_key])
    """

# 测试数据示例
def create_test_data():
    """
    创建测试用的二进制数据
    """
    import struct
    
    # 模拟 EOCalc 数据
    data = bytearray()
    
    # EOCalcVarCount = 2 (表示有2个结果项)
    data.extend(struct.pack('<B', 2))
    
    # 第一个 EOResultItem
    data.extend(struct.pack('<B', 1))      # VarIsOK = 1 (通过)
    data.extend(struct.pack('<f', 123.45)) # RealVar = 123.45
    data.extend(struct.pack('<f', 100.0))  # SetMinVar = 100.0
    data.extend(struct.pack('<f', 150.0))  # SetMaxVar = 150.0
    
    # 第二个 EOResultItem  
    data.extend(struct.pack('<B', 0))      # VarIsOK = 0 (失败)
    data.extend(struct.pack('<f', 89.12))  # RealVar = 89.12
    data.extend(struct.pack('<f', 90.0))   # SetMinVar = 90.0
    data.extend(struct.pack('<f', 110.0))  # SetMaxVar = 110.0
    
    return bytes(data)

def test_eo_calc_parsing():
    """
    测试 EOCalc 解析功能
    """
    print("=== EOCalc 解析测试 ===")
    
    # 创建测试数据
    test_data = create_test_data()
    print(f"测试数据长度: {len(test_data)} 字节")
    
    # 解析数据
    offset = 0
    temp_data = []
    
    try:
        offset = process_eo_calc_type(test_data, offset, temp_data)
        
        print("\n=== 解析结果 ===")
        for i in range(0, len(temp_data), 2):
            if i + 1 < len(temp_data):
                print(f"{temp_data[i]} {temp_data[i+1]}")
            else:
                print(f"{temp_data[i]}")
                
        print(f"\n解析完成，最终offset: {offset}")
        print(f"数据长度: {len(test_data)}")
        
        if offset == len(test_data):
            print("✅ 解析成功，所有数据都被正确读取")
        else:
            print("❌ 解析可能有问题，offset与数据长度不匹配")
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")

if __name__ == "__main__":
    test_eo_calc_parsing()
