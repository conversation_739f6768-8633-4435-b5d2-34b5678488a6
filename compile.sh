#!/bin/bash

# 编译脚本 - 用于编译数据库更新程序

echo "开始编译数据库更新程序..."

# 设置编译器和编译选项
CXX=g++
CXXFLAGS="-std=c++17 -Wall -Wextra -O2"

# 设置库路径（根据你的系统调整）
SQLITE_LIB="-lsqlite3"
PTHREAD_LIB="-lpthread"

# 设置包含路径（根据你的项目结构调整）
INCLUDE_PATHS="-I. -I./include"

# 源文件
SOURCES="update_exe_complete_v1.cpp main_example.cpp"

# 输出文件名
OUTPUT="database_updater"

# 编译命令
echo "执行编译命令..."
$CXX $CXXFLAGS $INCLUDE_PATHS $SOURCES $SQLITE_LIB $PTHREAD_LIB -o $OUTPUT

# 检查编译结果
if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo "可执行文件: $OUTPUT"
    echo ""
    echo "使用方法："
    echo "1. 确保数据库文件路径正确："
    echo "   - 旧数据库: /root/press.lx"
    echo "   - 新数据库: /root/press_new.lx"
    echo ""
    echo "2. 以root权限运行程序："
    echo "   sudo ./$OUTPUT"
    echo ""
    echo "3. 或者通过HTTP接口调用："
    echo "   curl -X POST http://localhost:8080/sys/misc/update"
else
    echo "❌ 编译失败！"
    echo ""
    echo "可能的解决方案："
    echo "1. 安装SQLite开发库："
    echo "   Ubuntu/Debian: sudo apt-get install libsqlite3-dev"
    echo "   CentOS/RHEL: sudo yum install sqlite-devel"
    echo ""
    echo "2. 安装httplib库（如果使用）："
    echo "   git clone https://github.com/yhirose/cpp-httplib.git"
    echo "   cp cpp-httplib/httplib.h ."
    echo ""
    echo "3. 确保所有依赖的头文件存在："
    echo "   - dbi.h (你的数据库接口)"
    echo "   - httplib.h (HTTP库)"
    exit 1
fi
