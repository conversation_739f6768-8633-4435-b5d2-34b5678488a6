#ifndef UPDATE_EXE_H
#define UPDATE_EXE_H

#include <sqlite3.h>
#include <string>
#include <vector>
#include <httplib.h>

// 前向声明
class DBI;

class update_exe {
private:
    DBI* dbi;

public:
    // 构造函数和析构函数
    update_exe(DBI* db);
    ~update_exe();
    
    // 公共接口
    void tableCheck();
    void bindServe(httplib::Server& svr);
    void handle(const httplib::Request& req, httplib::Response& res);
    
private:
    // 私有辅助函数
    bool execute_sql(sqlite3* db, const std::string& sql);
    bool detach_database_with_retry(sqlite3* db, const std::string& alias, int max_attempts = 5);
    void update_exe_tmp(DBI* db);
};

// 配置常量声明
extern const std::string OLD_DB_PATH;
extern const std::string NEW_DB_PATH;
extern const std::vector<std::string> TABLES_TO_PROCESS;

#endif // UPDATE_EXE_H
